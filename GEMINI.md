# GEMINI.md

This file provides guidance to <PERSON> when working with code in this repository.

## Project Overview

Irriga Mais is an agricultural irrigation management system that controls localized irrigation equipment through LoRa mesh networks. The system consists of hardware devices (LIC controllers, valve controllers, water pump controllers, reservoir monitors) and a full-stack web application for configuration and monitoring.

**Key Domain Concepts:**

- **LIC (Localized Irrigation Controller)**: Main internet-connected board that controls irrigation logic
- **VC (Valve Controller)**: Controls up to 4 electric irrigation valves
- **WPC (Water Pump Controller)**: Controls water pumps (PL10/PL50 variants)
- **RM (Reservoir Monitor)**: Monitors water reservoir systems
- **Property → Project → Sector**: Hierarchical structure where each property has irrigation projects, and each project has sectors with specific irrigation schedules

## Repository Structure

This is a monorepo with multiple packages:

### Frontend (`/app/`)

React 19 + TypeScript PWA using Bun runtime

- **Entry points**: `src/index.tsx` (server), `src/frontend.tsx` (client)
- **State**: Jotai atoms organized by domain in `src/store/`
- **Routing**: Wouter for client-side routing
- **Styling**: TailwindCSS v4 with custom design tokens
- **API**: Directus SDK client in `src/api/`

### Backend (`/directus/`)

Directus CMS with PostgreSQL and PostGIS

- **Database**: PostgreSQL with migrations in `migrations/`
- **Seeding**: Comprehensive seed data system in `src/seed/`
- **Testing**: Bun test runner with transaction-based cleanup

### Protocol (`/protobuf/`)

Protocol buffer definitions and generated TypeScript bindings

- **Source**: `.proto` files defining device communication protocol
- **Build**: `bun run build` generates TypeScript definitions in `dist/`

### Integration (`/mqtt-integration/`)

MQTT message handling service for device communication

- **Purpose**: Bridges protocol buffer messages with database operations
- **Database**: Direct PostgreSQL queries for device state management

## Common Development Commands

### Frontend Development

```bash
cd app/
bun install
bun run dev          # Development server on port 3000
bun run build        # Production build to dist/
bun run start        # Production server
```

### Backend Development

```bash
cd directus/
bun install
bun run docker:down-up           # Start full Docker stack
bun run seed:populate            # Populate with test data
bun run seed:cleanup             # Clean up test data
```

### Database Operations

```bash
# Migration management
bun run migration:create         # Create new migration
bun run docker:directus:cli:db:migrate:latest  # Apply migrations

# Database access
bun run docker:database:psql     # Direct PostgreSQL access
bun run docker:directus:recreate # Recreate Directus container
```

### Testing

```bash
cd directus/
bun test ./tests                 # Run all tests
bun test ./tests/mesh_device_mapping.test.ts  # Single test file
bun test ./tests --grep "substring"           # Filter by name
```

### Protocol Buffer Development

```bash
cd protobuf/
bun install
bun run build        # Generate TypeScript from .proto files
```

### Type Checking

```bash
# Check types in any package
bun x tsc -p app/ --noEmit
bun x tsc -p directus/ --noEmit
bun x tsc -p mqtt-integration/ --noEmit
```

## Development Conventions

### Architecture

- **State Management (Jotai)**: Atoms are organized by domain in `src/store/` and all atom names end with `Atom` suffix.
- **API Layer**: A centralized API service in `src/api/service.ts` uses the Directus SDK.
- **Component Architecture**: Components are organized by domain with a clear separation of concerns.
- **Database Design**: The database follows a hierarchical structure: Account → Property → Project → Sector.

### Code Style

- **TypeScript**: Strict mode is enabled, and `unknown` is used instead of `any`.
- **React**: Use functional components with hooks.
- **Styling (TailwindCSS)**: Use utility classes directly and follow the design system tokens.
- **Formatting**: 2-space indentation, single quotes, and semicolons.
- **Naming**: `camelCase` for variables/functions, `PascalCase` for components/types, `CONSTANT_CASE` for constants, and `kebab-case` for filenames.

### Testing

- **Database Testing**: All tests run in transactions with automatic rollback.
- **Seed Data Testing**: A comprehensive seed system tests business logic and constraints.

### Workflow

- **Git**: The base branch is `develop`. Feature branches are named `<task-list>-<task-number>-<description>`. Commits follow the Conventional Commits format.
- **Tasks**: Tasks are documented in the `tasks/` directory.

## General Guidelines (VERY IMPORTANT)

Guidelines are defined in subfolders of /docs/guidelines. See /docs/guidelines/00-INDEX.md for a list of all guidelines, this can help you find the relevant guidelines to understand the context of the task. For example, if working with the app folder, you should read the frontend guidelines.
