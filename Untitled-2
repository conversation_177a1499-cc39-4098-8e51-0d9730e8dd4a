# Irrigation Plan State Implementation

Implement a comprehensive irrigation plan state tracking system that processes SchedulingReportPackage MQTT messages and stores execution states in both current and historical database tables.

## Overview
Create database tables and MQTT processing logic to track irrigation plan execution states from device SchedulingReportPackage messages. The system should maintain both current state (latest execution per plan) and historical records with TimescaleDB integration.

## Protobuf Message Structure
The system processes SchedulingReportPackage messages with this structure:
```protobuf
message SchedulingReportData {
  int32  scheduling_idx = 1;       // Index of executed schedule
  uint64 start_time = 2;           // Schedule start timestamp
  uint64 end_time = 3;             // Schedule end timestamp
  uint64 sector_bitmask1 = 4;      // Activated sectors bitmask (first 64 bits)
  uint64 sector_bitmask2 = 5;      // Activated sectors bitmask (next 64 bits)
  uint64 ferti_bitmask1 = 6;       // Fertigation sectors bitmask (first 64 bits)
  uint64 ferti_bitmask2 = 7;       // Fertigation sectors bitmask (next 64 bits)
  bool   waterpump = 8;            // Water pump status during execution
  uint64 backwash_time = 9;        // Backwash start timestamp (if occurred)
  int32  number_of_sectors = 10;   // Number of sectors in schedule
  bool   had_waterpump = 11;       // Whether schedule uses water pump
  bool   had_ferti = 12;           // Whether schedule uses fertigation
  uint64 time_of_resumption = 13;  // Resumption start time
  int32  resumption_attempts = 14; // Number of resumption attempts
  int32  status = 15;              // Schedule status (1 = Completed)
}

message SchedulingReportPackage {
  repeated SchedulingReportData data = 1;
}
```

## Database Implementation Requirements

### 1. Table Structures

**current_irrigation_plan_state table:**
- id (uuid, primary key, auto-set to irrigation_plan value)
- irrigation_plan (uuid, foreign key to irrigation_plan table, unique constraint)
- packet_date (timestamptz, when device status packet was recorded)
- start_time (timestamptz, irrigation execution start time)
- end_time (timestamptz, irrigation execution end time, nullable)
- activated_steps (jsonb array, irrigation_plan_step IDs for activated sectors)
- activated_ferti_steps (jsonb array, irrigation_plan_step IDs for fertigation sectors)
- waterpump_working (boolean, water pump status during execution)
- backwash_start_time (timestamptz, backwash start time, nullable)
- uses_waterpump (boolean, whether schedule should use water pump)
- uses_ferti (boolean, whether schedule should use fertigation)
- date_created (timestamptz, record creation time)
- date_updated (timestamptz, record last update time)

**irrigation_plan_state table (historical):**
- Same fields as current_irrigation_plan_state except:
  - No unique constraint on irrigation_plan
  - No date_updated field
  - Composite primary key: (irrigation_plan, packet_date)
  - TimescaleDB hypertable with monthly chunking
  - Compression after 3 months

### 2. Business Logic Rules
- current_irrigation_plan_state holds only the most recent state per irrigation plan
- If end_time is null, irrigation plan is still running
- If end_time not in protobuf but status=1, set end_time to packet_date
- irrigation_plan_state populated via triggers on current_irrigation_plan_state changes
- Conflicting records in irrigation_plan_state should be ignored with warning logged

### 3. Required Database Migrations
1. Create both table structures
2. Create BEFORE INSERT trigger to auto-set current_irrigation_plan_state.id = irrigation_plan
3. Configure TimescaleDB hypertable for irrigation_plan_state
4. Create AFTER INSERT/UPDATE triggers to populate irrigation_plan_state from current_irrigation_plan_state
5. Create complete Directus configuration (collections, fields, permissions, display options)

## MQTT Integration Requirements

### 1. State Calculator (irrigation-plan-state-calculator.ts)
Create calculation logic similar to project-state-calculator.ts that:
- Maps scheduling_idx to irrigation plan using LICState.schedules array
- Converts sector bitmasks to irrigation_plan_step IDs using LICState.sectorSchedules
- Handles both activation and fertigation bitmasks
- Returns calculated state object without database operations

### 2. Message Processor (scheduling-report-package.ts)
Create processor similar to status-package.ts that:
- Uses irrigation-plan-state-calculator.ts for state calculation
- Handles database upsert operations
- Processes SchedulingReportPackage messages

### 3. Database Mutations (irrigation-plan-state.ts)
Create database mutation functions for:
- Upserting current_irrigation_plan_state records
- Handling conflict resolution
- Supporting the scheduling-report-package.ts processor

## Data Flow Logic
1. SchedulingReportData.scheduling_idx → match with LICState.schedules[].ord_idx → get irrigationPlanId
2. sector_bitmask1 bits → map to LICState.sectorSchedules with matching scheduling_idx and n_order → get irrigationPlanStepId array for activated_steps
3. ferti_bitmask1 bits → map to LICState.sectorSchedules with matching scheduling_idx and n_order → get irrigationPlanStepId array for activated_ferti_steps

## Deliverable
Create a comprehensive TASKS file in @/tasks/active following the structure defined in @/tasks/INSTRUCTIONS.md and @/.claude/commands/task-file-expanding.md. The file should contain one main task with properly expanded subtasks covering all database migrations, MQTT integration components, and Directus configuration requirements.