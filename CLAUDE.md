# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Irriga Mais is an agricultural irrigation management system that controls localized irrigation equipment through LoRa mesh networks. The system consists of hardware devices (LIC controllers, valve controllers, water pump controllers, reservoir monitors) and a full-stack web application for configuration and monitoring.

**Key Domain Concepts:**

- **L<PERSON> (Localized Irrigation Controller)**: Main internet-connected board that controls irrigation logic
- **VC (Valve Controller)**: Controls up to 4 electric irrigation valves
- **WPC (Water Pump Controller)**: Controls water pumps (PL10/PL50 variants)
- **RM (Reservoir Monitor)**: Monitors water reservoir systems
- **Property → Project → Sector**: Hierarchical structure where each property has irrigation projects, and each project has sectors with specific irrigation schedules

## Repository Structure

This is a monorepo with multiple packages:

### Frontend (`/app/`)

React 19 + TypeScript PWA using Bun runtime

- **Entry points**: `src/index.tsx` (server), `src/frontend.tsx` (client)
- **State**: Jotai atoms organized by domain in `src/store/`
- **Routing**: Wouter for client-side routing
- **Styling**: TailwindCSS v4 with custom design tokens
- **API**: Directus SDK client in `src/api/`

### Backend (`/directus/`)

Directus CMS with PostgreSQL and PostGIS

- **Database**: PostgreSQL with migrations in `migrations/`
- **Seeding**: Comprehensive seed data system in `src/seed/`
- **Testing**: Bun test runner with transaction-based cleanup

### Protocol (`/protobuf/`)

Protocol buffer definitions and generated TypeScript bindings

- **Source**: `.proto` files defining device communication protocol
- **Build**: `bun run build` generates TypeScript definitions in `dist/`

### Integration (`/mqtt-integration/`)

MQTT message handling service for device communication

- **Purpose**: Bridges protocol buffer messages with database operations
- **Database**: Direct PostgreSQL queries for device state management

## Common Development Commands

### Frontend Development

```bash
cd app/
bun install
bun run dev          # Development server on port 3000
bun run build        # Production build to dist/
bun run start        # Production server
```

### Backend Development

```bash
cd directus/
bun install
bun run docker:down-up           # Start full Docker stack
bun run seed:populate            # Populate with test data
bun run seed:cleanup             # Clean up test data
```

### Database Operations

```bash
# Migration management
bun run migration:create         # Create new migration
bun run docker:directus:cli:db:migrate:latest  # Apply migrations

# Database access
bun run docker:database:psql     # Direct PostgreSQL access
bun run docker:directus:recreate # Recreate Directus container
```

### Testing

```bash
cd directus/
bun test ./tests                 # Run all tests
bun test ./tests/mesh_device_mapping.test.ts  # Single test file
bun test ./tests --grep "substring"           # Filter by name
```

### Protocol Buffer Development

```bash
cd protobuf/
bun install
bun run build        # Generate TypeScript from .proto files
```

### Type Checking

```bash
# Check types in any package
bun x tsc -p app/ --noEmit
bun x tsc -p directus/ --noEmit
bun x tsc -p mqtt-integration/ --noEmit
```

## Architecture Guidelines

### State Management (Jotai)

- **Organization**: Atoms organized by domain in `src/store/` (auth.ts, data.ts, crud.ts, operations.ts, ui.ts)
- **Naming**: All atom names end with `Atom` suffix
- **Patterns**: Use derived atoms for computed state, write-only atoms for async operations
- **Usage**: `useAtomValue` for reading, `useSetAtom` for writing, `useAtom` for both

### API Layer

- **Service**: Centralized API service in `src/api/service.ts` using Directus SDK
- **Models**: Typed interfaces in `src/api/model/` for all database entities
- **Client**: Singleton Directus client with auth token management
- **Error Handling**: Standardized error handling with operation tracking

### Component Architecture

- **Structure**: Domain-organized components with clear separation of concerns
- **Pages**: Route components in `src/pages/` with nested structure
- **Components**: Reusable UI components in `src/components/`
- **Hooks**: Custom hooks in `src/hooks/` for reusable logic

### Database Design

- **Entities**: Account → Property → Project → Sector hierarchy
- **Devices**: Device management with property associations and mesh network mappings
- **Constraints**: Strict referential integrity with business rule enforcement
- **Migrations**: Versioned schema changes with Directus metadata management

## Code Style and Conventions

### TypeScript

- **Strict mode**: Enabled across all packages
- **Types**: Prefer interfaces for object shapes, union types over enums
- **Safety**: Avoid `any`, use `unknown` and explicit type narrowing

### React Patterns

- **Components**: Functional components with hooks only
- **Props**: TypeScript interfaces for all prop definitions
- **Performance**: Use `useCallback`/`useMemo` when needed for optimization
- **Keys**: Always provide keys for list rendering

### Styling (TailwindCSS)

- **Utilities**: Use utility classes directly, avoid `@apply` except for repeated patterns
- **Tokens**: Follow design system tokens defined in `docs/guidelines/references/design-tokens.md`
- **Responsive**: Mobile-first approach with responsive variants

### Code Formatting

- **Indentation**: 2 spaces
- **Quotes**: Single quotes for strings
- **Semicolons**: Always include semicolons
- **Imports**: Sort external before internal, clean unused imports

### Naming Conventions

- **Variables/Functions**: camelCase
- **Components/Types**: PascalCase
- **Constants**: CONSTANT_CASE
- **Files**: kebab-case for regular files, PascalCase for React components

## Testing Strategy

### Database Testing

- **Transactions**: All tests run in transactions with automatic rollback
- **Helpers**: Use transaction helpers in `tests/helpers/` for setup/teardown
- **Isolation**: Each test creates its own isolated test data
- **Cleanup**: Tests clean up after themselves using database transactions

### Seed Data Testing

- **Purpose**: Comprehensive seed system tests business logic and constraints
- **Usage**: `bun run seed:populate` for development data
- **Cleanup**: `bun run seed:cleanup` removes only seed-generated data

## Security and Environment

### Environment Variables

- **Client**: Use `BUN_PUBLIC_` prefix for client-visible variables
- **Server**: Database credentials and API keys server-side only
- **Secrets**: Never commit secrets, use environment-specific configuration

### Authentication

- **Flow**: Directus-based authentication with JWT tokens
- **Storage**: Secure token storage in client applications
- **Permissions**: Role-based access control through Directus permissions

## Development Workflow

### Git and Branching

- **Base Branch**: `develop` (current branch for new features)
- **Feature Branches**: Format: `<task-list>-<task-number>-<description>`
- **Commits**: Follow Conventional Commits format (feat:, fix:, chore:, etc.)

### Task Management

- **Documentation**: Tasks documented in `tasks/` directory
- **Branching**: One task per branch, scoped changes
- **References**: Include task references in commit messages

### Code Review Process

- **Quality**: Ensure type coverage and clean imports
- **Testing**: Run appropriate tests before merging
- **Documentation**: Update relevant documentation for architectural changes

## Integration and Deployment

### Docker

- **Services**: Full stack runs via Docker Compose
- **Images**: Separate images for app and Directus (`docker:build`, `docker:push` scripts)
- **Development**: Use `docker:down-up` for local development stack

### Database Migration

- **Versioning**: Sequential migration files with descriptive names
- **Schema**: PostGIS-enabled PostgreSQL with spatial data support
- **Directus**: Metadata migrations for CMS configuration changes

## Domain-Specific Guidelines

### Irrigation System Logic

- **Hardware Mapping**: Devices must be properly mapped to properties via mesh network constraints
- **Scheduling**: Irrigation plans execute based on time and day-of-week schedules
- **Water Flow**: One sector per project can be irrigated at a time
- **Constraints**: Strict business rules enforced at database level

### Device Communication

- **Protocol**: Protocol buffer definitions in `protobuf/` package
- **MQTT**: Message handling through `mqtt-integration/` service
- **State**: Device state synchronized between hardware and database

### Multi-tenancy

- **Structure**: Account-based isolation with user-account relationships
- **Permissions**: Property-level access control
- **Data**: Hierarchical data access patterns (Account → Property → Project → Sector)

## General Guidelines (VERY IMPORTANT)

Guidelines are defined in subfolders of /docs/guidelines. See /docs/guidelines/00-INDEX.md for a list of all guidelines, this can help you find the relevant guidelines to understand the context of the task. For example, if working with the app folder, you should read the frontend guidelines.
