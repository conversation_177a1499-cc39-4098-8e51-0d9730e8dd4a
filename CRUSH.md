CRUSH quick reference for Irriga Mais (Bun + React + Directus + Proto)

Build/run
- Root: multi-package. Use Bun.
- Frontend app/: bun install; bun run dev; bun run build; bun start
- Directus directus/: bun install; bun run docker:down-up (local stack); seed: bun run seed:populate
- Protobuf protobuf/: bun install; bun run build (generates dist from proto/*.proto)
- MQTT mqtt-integration/: bun install; bun run index.ts

Tests
- Directus uses Bun test runner in directus/tests/*.test.ts
- Run all: bun test ./directus/tests
- Run single file: bun test ./directus/tests/mesh_device_mapping.test.ts
- Filter by name: bun test ./directus/tests --grep "substring"
- DB for tests: ensure TEST_DB_* env vars or use docker:database:recreate-test-db

Lint/typecheck/format
- No eslint config found; rely on tsc strictness and editor formatters
- Typecheck app: bun x tsc -p app/ --noEmit; same for directus/, protobuf/, mqtt-integration/
- Format: use Prettier defaults if present in editor; keep two-space indent, semicolons, single quotes

Imports & modules
- ESM only; moduleResolution "bundler"; JSX "react-jsx"
- Prefer named exports; index barrels exist in several folders
- Import order: external libs, then internal ("@/*" alias in app), then assets/styles

Types & naming
- Strict TypeScript; avoid any; prefer unknown and explicit narrowing
- Interfaces for object shapes/props; union string literals over enums for closed sets
- camelCase variables/functions; PascalCase components/types; CONSTANT_CASE env keys

React & state (app/)
- Functional components with hooks; colocate domain atoms in src/store; use useAtomValue/useSetAtom
- Use Tailwind utilities; keep className concise; follow docs/design.json tokens

Error handling
- Wrap async with try/catch; return Result-like objects in services; never swallow errors silently
- For DB tests, use transactions and rollback helpers in directus/tests/helpers

Security & env
- Never commit secrets; client-visible env must be BUN_PUBLIC_*
- Do not log tokens or passwords

Other tips
- Proto changes: run protobuf build, then bump dependency in mqtt-integration if needed
- Docker images: app and directus have docker:build/docker:push scripts
