# Task list info:

- name: 20250829_01
- base_branch: develop

---

# Tasks

## Task 1. Project state calculation and storage

**Description**
The message SystemStatusPackage sent by LIC devices contains some bitmask fields.
Each bit in the bitmask represents a specific device.
The lic_state table contains a jsonb column called devices, which is an array of internal LIC devices.
The devices array in lic_state is generated in the mqtt-integration package based on the data in the database. It is generated in the CodecManager class by calling the loadLICStateByIdentifier function in the db-loader module.
loadLICStateByIdentifier returns LICState type. Each property of LICState is a jsonb field in the lic_state table.
With that information, we can decode the bitmask fields in the SystemStatusPackage to get the list of active devices, failed devices, etc.
Each device in the jsonb devices array of lic_state has a ord_idx property. This is the index of the device in the bitmask. Thus, to check if a device is set in a bitmask, we can check if the bit in the bitmask at the index of the device's ord_idx is set to 1. For this we can perform a bitwise AND operation between the bitmask and 1 << device.ord_idx.
These are the bitmask fields in the SystemStatusPackage:

- `sync_bitmask` - Bitmask of devices successfully synchronized
- `on_bitmask` - Bitmask of currently active/powered devices
- `input_bitmask` - Bitmask of device input states
- `failed_bitmask` - Bitmask indicating device failures

To know if a element in the Irriga+ system is, for example, active, we need to check if the device that controls it is active. The same applies to the other states.
The items in the jsonb devices array of lic_state have an elementId property and an elementType property. This is the id of the element in the Irriga+ system that the device controls. It can also contain a elementVariation property, which is the variation of the element. Currently the variation is only used for pumps, to distinguish between irrigation or backwash. For example, If a pump is o f type irrigation and the project using the pump has backwash_pump_type as IRRIGATION, there will be two devices in the devices array for that pump. One with elementType as "pump" and elementVariation as "irrigation" and another with elementType as "pump" has no elementVariation.

These are the elementTypes:

- "valve" -> sector table
- "pump" -> water_pump table
- "reservoir" -> reservoir table

I want to take the SystemStatusPackage and calculate the irrigation project (project table) state.
The state of the project would be somthing like:

type ProjectState = {
// PK
id: uuid;
// FK to project table
project: uuid;
irrigation_status: "active" | "inactive" | "error";
fertigation_status: "active" | "inactive" | "error";
backwash_status: "active" | "inactive" | "error";
sectors: Array<{
// FK to sector table
sector: uuid;
sector_name: string;
status: "active" | "inactive" | "error";
}>; // JSONBArray of sector states  
}

We would need a table to store the current project state, called current_project_state, and a timescaledb hyper table to store the project state history, called project_state. The current_project_state table would have a one-to-one relationship with the project table. The project_state table would have a one-to-many relationship with the project table. Similar to current_lic_packet and lic_packet, the project_state table would be populated by a trigger on current_project_state.

Since one LIC can be associated to many projects, we would need to calculate the project state for each project associated to the LIC. We would need to check which device belongs to each project and calculate the state based on that. Let's call this function calculateProjectsState, which would receive the SystemStatusPackage and the lic_state as parameters and return an array of ProjectState. calculateProjectsState would also need to know which project each device belongs to. The getLICTree function in the packages/mqtt-integration/src/db/queries/lic-queries.ts module already returns the LIC tree, which contains the projects and the devices. calculateProjectsState would receive the LIC tree as a parameter and traverse it to calculate the project states.
A algorithm suggestion for this calculation would be:

- iterate through the projects in the LIC tree
- Each project contains its irrigation water pump including the water controller device associated to it, fertigation water pump including the water controller device associated to it, backwash_pump_type and the sectors. We would need to find the devices in the SystemStatusPackage that correspond to each of these elements and check their state.

LIC tree also contains the reservoirs and service pumps. The state of these elements is not relevant to the project state, but it can be used later to calculate the state of each reservoir automation.

This calculation would be performed by the mqtt-integration package and the data would be stored in the database by the mqtt-integration package as well.
CodecManager would be responsible for starting the calculation by calling calculateProjectsState and storing the data in the database. It would be done in the handleMessage function, similar to how processInfoPackage is called.

In the frontend app we would need to query the current_project_state table to get the current state of the projects. For this we need to create the current_project_state model in app/src/api/model/current-project-state.ts and add the collection to AppSchema in app/src/api/client.ts. We would also need to add CRUD operations to apiService in app/src/api/service.ts and a jotai store in app/src/store/data.ts. Finally, we would need to create a query to load the current_project_state data for all projects in the selected property in app/src/api/queries/project.ts.

We would also need to document the new tables and fields in the database in docs/001-ENTITIES.md, docs/002-ENTITY_DIAGRAMS.md, docs/DDL.md and any other relevant documentation.

When creating the migration for the new tables, we need to create the directus configuration as well: fields, permissions, display options, collections, etc.

**Target directories**

- mqtt-integration (backend)
- directus (backend)
- app (frontend)
- docs (documentation)

**Status:** Pending

### Subtask 1.1. Database Schema and Migrations

**Description**
Create migration file for `current_project_state` table with fields: id (uuid), project (uuid FK), irrigation_status, fertigation_status, backwash_status, sectors (jsonb array), audit fields. Create TimescaleDB hypertable `project_state` with same schema plus time partitioning. Add trigger to populate `project_state` from `current_project_state` updates (similar to current_lic_packet → lic_packet pattern). Create indexes for performance: (project), (project, date_created). Add Directus configuration: collections, fields, permissions, display options. Follow existing migration patterns from 20250822A-create-current-lic-packet-table.js

**Target directories**

- directus (backend)

**Status:** Pending

### Subtask 1.2. Backend State Calculation Logic

**Description**
Create `calculateProjectsState` function in mqtt-integration/src/irriganet/. Function signature: `(systemStatusPackage: SystemStatusPackage, licState: LICState, licTree: LICTree) => ProjectState[]`. Implement bitmask decoding logic using `(bitmask & (1 << device.ord_idx)) !== 0` pattern. Map device states to project elements: irrigation_water_pump → irrigation_status, fertigation_water_pump → fertigation_status, sectors → sector states. Handle backwash_pump_type logic (IRRIGATION vs FERTIGATION vs null). Status determination: "active" (on_bitmask), "error" (failed_bitmask), "inactive" (default). Include comprehensive error handling and logging

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 1.3. CodecManager Integration

**Description**
Modify `CodecManager.handleMessage()` to detect SystemStatusPackage payloads. Add condition: `if (payloadType === "system_status" && payloadData)`. Call `calculateProjectsState(payloadData.system_status, this.state, licTree)`. Integrate with existing `getLICTree()` function from lic-queries.ts. Store calculated states using new mutation functions. Follow existing patterns from `processInfoPackage()` implementation. Add debug logging similar to existing message handling

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 1.4. Database Operations and Mutations

**Description**
Create `insertCurrentProjectState()` in mqtt-integration/src/db/mutations/. Create `updateCurrentProjectState()` for upsert operations. Implement batch operations for multiple project states from single LIC. Add proper error handling and transaction management. Follow existing patterns from current-lic-packet.ts mutations. Include TypeScript interfaces for ProjectStateInsert/Update types. Add database connection management using existing db container

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 1.5. Frontend API Models and Services

**Description**
Create `current-project-state.ts` model in app/src/api/model/ with ProjectState interface. Add `current_project_state: CurrentProjectState[]` to AppSchema in client.ts. Implement CRUD operations in apiService.ts: `getCurrentProjectStates()`, `getCurrentProjectStatesByProperty()`. Add proper TypeScript relations types following existing patterns. Include sector status array typing with proper DirectusRelationArrayType. Follow existing model patterns from project.ts and current-lic-packet.ts

**Target directories**

- app (frontend)

**Status:** Pending

### Subtask 1.6. Frontend State Management and Queries

**Description**
Create `currentProjectStatesAtom` in app/src/store/data.ts using existing patterns. Add derived atoms for filtering by property: `currentProjectStatesByPropertyAtom`. Create query function `loadCurrentProjectStatesByProperty()` in app/src/api/queries/project.ts. Implement real-time updates integration with existing polling mechanisms. Add loading states and error handling atoms. Follow existing Jotai patterns from current-lic-packet implementations

**Target directories**

- app (frontend)

**Status:** Pending

### Subtask 1.7. Documentation Updates

**Description**
Update docs/001-ENTITIES.md: Add current_project_state and project_state table descriptions. Update docs/002-ENTITY_DIAGRAMS.md: Add ERD relationships project ↔ current_project_state ↔ project_state. Update docs/DDL.md: Document new table schemas, indexes, triggers, and constraints. Document ProjectState TypeScript interface and field meanings. Document calculateProjectsState algorithm and bitmask decoding logic. Add SystemStatusPackage integration flow documentation. Follow existing documentation patterns and formatting

**Target directories**

- docs (documentation)

**Status:** Pending

---
