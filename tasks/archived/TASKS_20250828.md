# Task list info:

- name: 250828_01
- base_branch: develop

---

## Task 1. LIC state table and service

**Description**

# Task: Store LIC state

The protobuf InfoPackage message contains information about the update time of each configuration type.
These are the relevant fields:

- uint32 devices_id = 8; // Última atualização dos dispositivos
- uint32 scheduling_id = 9; // Última atualização dos agendamentos
- uint32 dev_scheduling_id = 10; // Última atualização dos agendamentos por dispositivo
- uint32 automation_id = 11; // Última atualização das automações
- uint32 config_id = 12; // Última atualização da configuração

Note: dev_scheduling_id will be always the same as scheduling_id, as both are updated in the same message.

the table lic_packet contains the complete history of all packets received from the LIC, thus, we can use it to keep track of the last update time of each configuration type stored in the LIC.
lic_packet has a payload_type column. When payload_type ins "info", the payload_data column will contain the InfoPackage message. Thus, we can get the last update time of each configuration type of a given LIC by querying the lic_packet table for the last packet with payload_type "info": the payload_data column will contain the parsed InfoPackage message as jsonb.

The table device_message_request contains the information about the requests sent to the LIC. It holds the complete history of all requests sent to the LIC, including the request payload_type, payload_data (parameter used to generate the request), and the status of the request. the packet_id column in device_message_request table is a timestamp, and it is also the id of the IncomingPacket message that is sent to the LIC. This packet_id is the value that the LIC uses to correlate the request and the response.

Example:

- device_message_request with payload_type="devices" and packet_id=1756327595 is created.
- mqtt-integration create the protobuf message and sends the request to the LIC.
- The LIC processes the request and updates its internal state. It will store the timestamp 1756327595 in the "devices_id" field.
- When the LIC sends the InfoPackage message, the "devices_id" field will contain the value 1756327595.
- InfoPackage is only sent by the LIC in response to a RequestInfoPackage message.

We need to detect out of sync states. For each field corresponding to a configuration type in InfoPackage, we need to compare the value with the last request sent to the LIC.
If the value is smaller than the last request, it means that the LIC did not process the request and we say that the configuration is "BEHIND". If the value is equal to the last request, it means that the LIC processed the request and we say that the configuration is "IN_SYNC". If the value is greater than the last request, it means that the LIC processed the request and also received a request from another source and we say that the configuration is "AHEAD".

We need to create a table to store the current state of LIC's:

- It will be called lic_state.
- It will have its PK as a device table FK, thus, we have one record for each LIC.
- It will store each property of LICState (packages/mqtt-integration/src/irriganet/db-loader/types.ts) as a jsonb column (lic, groups, devices, meshDevices, schedules, sectorSchedules, deviceSchedules)
- It will store the last request timestamp sent to the LIC for each configuration type (devices,scheduling,dev_scheduling,automation,config).
- It will store the current timestamp of each configuration received in the last InfoPackage received from the LIC (devices,scheduling,dev_scheduling,automation,config).

The act of populating this table will be done in "mqtt-integration" package, as it is the one that handles the communication with the LIC and have access to the database.

- When a request is sent to the LIC by the CodecManager in mqtt-integration, lic_state will be updated (or created if a record does not exist yet) with the last request timestamp for the corresponding configuration type.
- When a InfoPackage is received from the LIC, lic_state will be updated with the current timestamp of each configuration.
- When the setState function of CodecManager is called, lic_state will be updated with the LICState object.

The table creation will be a migration in the "directus" package.

**Target directories**

- mqtt-integration (backend)
- directus (backend)

**Status:** Done

### Subtask 1.1. Create lic_state table migration

**Description**
Create migration file to implement the lic_state table with all required columns for storing LICState objects and tracking configuration sync timestamps. The table will have device as FK primary key and include JSONB columns for each LICState property plus timestamp tracking columns for sync detection.
Make sure to include directus configuration for the new table: fields, permissions, display options, collections, etc.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.2. Implement database mutations and queries

**Description**
Create TypeScript functions for CRUD operations on lic_state table, including upsert operations for state updates and query functions for retrieving current sync status. Follow existing patterns from current-lic-packet and device-message-request database functions.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 1.3. Integrate with CodecManager for state tracking

**Description**
Modify CodecManager to update lic_state when sending requests and receiving InfoPackage messages, implementing the core sync detection logic (BEHIND/IN_SYNC/AHEAD). Update setState function and add InfoPackage processing to track configuration timestamps.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 1.4. Update documentation and Directus configuration

**Description**
Update required documentation files per project guidelines and create Directus configuration migration for the new table to make it accessible through the admin interface.

**Target directories**

- directus (backend)

**Status:** Done
