#!/usr/bin/env bun

/**
 * Node.js CLI to manage TASKS files.
 *
 * Usage:
 *   node tasks/cli.ts [--tasks-dir <dir>] create [optional-name] [--base-branch <branch>]
 *   node tasks/cli.ts [--tasks-dir <dir>] list [--all] [--archived]
 *   node tasks/cli.ts [--tasks-dir <dir>] archive <name>
 *
 * Behavior:
 * - create:
 *   - If a name is provided, use it (sanitize, ensure .md). If file exists, append -01, -02, ...
 *   - If no name, generate TASKS_YYMMDD_XX.md where XX starts at 01 and increments based on existing files for today.
 *   - Writes scaffold markdown content per tasks/INSTRUCTIONS.md format.
 *   - Supports optional --base-branch to set the base_branch field in the scaffold (default: develop).
 *   - Prints absolute path to created file.
 * - list:
 *   - Default lists active files in <tasks-dir>/active.
 *   - --archived lists only files in <tasks-dir>/archived.
 *   - --all lists both sections.
 * - archive:
 *   - Move a file from <tasks-dir>/active to <tasks-dir>/archived.
 *   - If destination exists, append -01, -02, ...
 */

import fs from "fs/promises";
import path from "path";
import { parseArgs } from "util";

type CreateOptions = {
  providedName?: string;
  tasksDirPath: string;
  baseBranch?: string;
};

function pad2(n: number) {
  return n.toString().padStart(2, "0");
}

function getTodayYYMMDD(date = new Date()) {
  const yy = date.getFullYear().toString().slice(-2);
  const mm = pad2(date.getMonth() + 1);
  const dd = pad2(date.getDate());
  return `${yy}${mm}${dd}`;
}

function sanitizeBaseName(name: string): string {
  const base = path.basename(name);
  const withoutExt = base.replace(/\.md$/i, "");
  const sanitized = withoutExt
    .trim()
    .replace(/[^a-zA-Z0-9._-]+/g, "-")
    .replace(/-+/g, "-")
    .replace(/^[-_.]+|[-_.]+$/g, "");
  return sanitized || "TASKS";
}

function ensureMdExtension(name: string): string {
  return name.toLowerCase().endsWith(".md") ? name : `${name}.md`;
}

async function pathExists(p: string): Promise<boolean> {
  try {
    await fs.stat(p);
    return true;
  } catch {
    return false;
  }
}

async function ensureDir(dirPath: string) {
  await fs.mkdir(dirPath, { recursive: true });
}

async function listMarkdownFiles(dirPath: string): Promise<string[]> {
  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });
    return entries
      .filter((d) => d.isFile() && d.name.toLowerCase().endsWith(".md"))
      .map((d) => d.name);
  } catch {
    return [];
  }
}

async function nextCounterForToday(
  todayYYMMDD: string,
  tasksDirPath: string
): Promise<string> {
  const files = await listMarkdownFiles(tasksDirPath);
  const regex = new RegExp(`^TASKS_${todayYYMMDD}_(\\d{2})\\.md$`, "i");
  const counters = files
    .map((f) => {
      const m = f.match(regex);
      // Guard parseInt input to always be string to satisfy TS
      return m ? parseInt(String(m[1]), 10) : null;
    })
    .filter((n): n is number => n !== null)
    .sort((a, b) => a - b);
  const next = (counters.at(-1) ?? 0) + 1;
  return pad2(next);
}

async function resolveUniqueName(
  base: string,
  dirPath: string
): Promise<string> {
  let candidate = ensureMdExtension(base);
  let idx = 1;
  while (await pathExists(path.join(dirPath, candidate))) {
    const baseNoExt = candidate.replace(/\.md$/i, "");

    // Check if this follows TASKS_YYMMDD_NN pattern and increment NN
    const tasksPattern = /^(TASKS_\d{6})_(\d{2})$/;
    const match = baseNoExt.match(tasksPattern);

    if (match && match[1] && match[2]) {
      const prefix = match[1];
      const currentNum = parseInt(match[2], 10);
      const nextNum = currentNum + 1;
      candidate = `${prefix}_${pad2(nextNum)}.md`;
    } else {
      // For other patterns, use the existing suffix logic
      const suffix = `-${pad2(idx)}`;
      const nextBase = baseNoExt.replace(/-\d{2}$/, "") + suffix;
      candidate = `${nextBase}.md`;
      idx += 1;
    }
  }
  return candidate;
}

function scaffoldContent(taskBaseName: string, baseBranch: string): string {
  return `# Task list info:

- name: ${taskBaseName}
- base_branch: ${baseBranch}

---

# Task list context: <optional>

Provide any relevant context for the tasks listed below.

---

# Tasks

## Task 1. Brief description of the task

**Description**
Detailed description of the task

**Target directories**

- <directory-1> (<directory-description>)
- <directory-2> (<directory-description>)

**Status:** Pending|In Progress|Done|Canceled|Error
`;
}

function extractTaskBaseName(filename: string): string {
  // Expect filename like "TASKS_YYMMDD_XX.md" or sanitized custom names.
  // Rule: task name should be filename minus leading "TASKS_" and trailing ".md".
  const base = filename.replace(/\.md$/i, "");
  const withoutPrefix = base.replace(/^TASKS_/, "");
  return withoutPrefix;
}

async function createCommand(opts: CreateOptions) {
  // Ensure tasks root and subfolders exist
  await ensureDir(opts.tasksDirPath);
  const activeDir = path.join(opts.tasksDirPath, "active");
  await ensureDir(activeDir);

  let filename: string;
  if (opts.providedName && opts.providedName.trim().length > 0) {
    const sanitized = sanitizeBaseName(opts.providedName);
    filename = await resolveUniqueName(sanitized, opts.tasksDirPath);
  } else {
    const yymmdd = getTodayYYMMDD();
    const counter = await nextCounterForToday(yymmdd, opts.tasksDirPath);
    filename = `TASKS_${yymmdd}_${counter}.md`;
  }

  // Create inside active folder
  let filePath = path.resolve(activeDir, filename);
  if (await pathExists(filePath)) {
    const unique = await resolveUniqueName(
      filename.replace(/\.md$/i, ""),
      activeDir
    );
    filename = unique;
    filePath = path.resolve(activeDir, filename);
  }

  const taskBaseName = extractTaskBaseName(filename);
  const baseBranch = opts.baseBranch?.trim() || "develop";
  await fs.writeFile(
    filePath,
    scaffoldContent(taskBaseName, baseBranch),
    "utf8"
  );
  process.stdout.write(filePath + "\n");
}

function printUsage() {
  console.error(`Usage:
  node tasks/cli.ts [--tasks-dir <dir>] create [optional-name] [--base-branch <branch>]
  node tasks/cli.ts [--tasks-dir <dir>] list [--all] [--archived]
  node tasks/cli.ts [--tasks-dir <dir>] archive <name>

Global options:
  --tasks-dir <dir>   Directory where TASKS files are stored. Default: tasks

Examples:
  node tasks/cli.ts create
  node tasks/cli.ts create --base-branch release/1.2.3
  node tasks/cli.ts --tasks-dir my-tasks create
  node tasks/cli.ts create TASKS_custom-integration
  node tasks/cli.ts --tasks-dir ./other-tasks "feature tasks"
  node tasks/cli.ts list
  node tasks/cli.ts list --archived
  node tasks/cli.ts list --all
  node tasks/cli.ts archive TASKS_250101_01
`);
}

async function listCommand(
  tasksDirPath: string,
  flags: { all?: boolean; archived?: boolean }
) {
  const activeDir = path.join(tasksDirPath, "active");
  const archivedDir = path.join(tasksDirPath, "archived");

  const wantActive = flags.all || !flags.archived;
  const wantArchived = flags.all || flags.archived;

  const [active, archived] = await Promise.all([
    wantActive
      ? listMarkdownFiles(activeDir).then((files) =>
          files.filter((f) => f.startsWith("TASKS_"))
        )
      : Promise.resolve([]),
    wantArchived
      ? listMarkdownFiles(archivedDir).then((files) =>
          files.filter((f) => f.startsWith("TASKS_"))
        )
      : Promise.resolve([]),
  ]);

  active.sort((a, b) => a.localeCompare(b));
  archived.sort((a, b) => a.localeCompare(b));

  if (flags.all) {
    if (active.length > 0) {
      process.stdout.write("Active:\n");
      for (const f of active) process.stdout.write(f + "\n");
    } else {
      process.stdout.write("Active:\n");
    }
    if (archived.length > 0) {
      process.stdout.write("Archived:\n");
      for (const f of archived) process.stdout.write(f + "\n");
    } else {
      process.stdout.write("Archived:\n");
    }
    return;
  }

  if (flags.archived) {
    for (const f of archived) process.stdout.write(f + "\n");
    return;
  }

  for (const f of active) process.stdout.write(f + "\n");
}

async function archiveCommand(tasksDirPath: string, nameArg: string) {
  if (!nameArg) {
    console.error("Error: archive requires <name> argument.");
    printUsage();
    process.exit(1);
  }

  await ensureDir(tasksDirPath);
  const activeDir = path.join(tasksDirPath, "active");
  const archivedDir = path.join(tasksDirPath, "archived");
  await ensureDir(activeDir);
  await ensureDir(archivedDir);

  const normalizedName = ensureMdExtension(path.basename(nameArg as string));
  const srcPath = path.resolve(activeDir, normalizedName);
  const archivedPath = path.resolve(archivedDir, normalizedName);

  const inActive = await pathExists(srcPath);
  const alreadyArchived = await pathExists(archivedPath);

  if (!inActive) {
    if (alreadyArchived) {
      console.error("Error: File already archived.");
      process.exit(1);
    }
    console.error("Error: File not found in active tasks directory.");
    process.exit(1);
  }

  // Resolve unique name in archived directory if needed
  let destName = normalizedName;
  if (await pathExists(archivedPath)) {
    destName = await resolveUniqueName(
      normalizedName.replace(/\.md$/i, ""),
      archivedDir
    );
  }
  const destPath = path.resolve(archivedDir, destName);

  await fs.rename(srcPath, destPath);
  process.stdout.write(destPath + "\n");
}

async function main() {
  // Define top-level options
  const { values, positionals } = parseArgs({
    args: process.argv.slice(2),
    options: {
      "tasks-dir": { type: "string" },
      help: { type: "boolean", short: "h" },
      // list subcommand flags can appear after list; we accept them globally and validate per subcommand
      all: { type: "boolean" },
      archived: { type: "boolean" },
      "base-branch": { type: "string" },
    },
    allowPositionals: true,
    strict: false, // we'll handle unknowns per subcommand to preserve behavior
  });

  // Handle global help
  if (values.help) {
    printUsage();
    process.exit(0);
  }

  const tasksDir = (values["tasks-dir"] as string | undefined) ?? "tasks";
  const sub = positionals[0];

  if (!sub) {
    printUsage();
    process.exit(1);
  }
  if (sub === "--help" || sub === "-h") {
    printUsage();
    process.exit(0);
  }

  const tasksDirPath = path.resolve(process.cwd(), tasksDir);

  switch (sub) {
    case "create": {
      // create [optional-name] [--base-branch <branch>]
      const providedName: string = positionals[1] ?? "";
      const baseBranch =
        (values["base-branch"] as string | undefined) ?? "develop";
      await createCommand({ providedName, tasksDirPath, baseBranch });
      break;
    }
    case "list": {
      // list [--all] [--archived]
      // Accept flags from values (parsed globally). Also validate any extra unknown positionals beyond subcommand.
      // Any additional positionals beyond the subcommand are invalid for list.
      const extra = positionals.slice(1).filter((p) => p && !p.startsWith("-"));
      if (extra.length > 0) {
        console.error(`Unknown option for list: ${extra[0]}`);
        printUsage();
        process.exit(1);
      }
      const all = Boolean(values.all);
      const archived = Boolean(values.archived);
      await listCommand(tasksDirPath, { all, archived });
      break;
    }
    case "archive": {
      // archive <name>
      const nameArg = positionals[1] ?? "";
      if (!nameArg) {
        console.error("Error: archive requires <name> argument.");
        printUsage();
        process.exit(1);
      }
      await archiveCommand(tasksDirPath, nameArg);
      break;
    }
    default: {
      console.error(`Unknown command: ${sub}`);
      printUsage();
      process.exit(1);
    }
  }
}

void main();
