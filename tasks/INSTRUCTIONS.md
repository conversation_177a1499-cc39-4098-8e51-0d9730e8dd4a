# Instructions for executing a task

The TASKS file is used to specify and track the progress of tasks. It is a markdown file with a specific format:

```md
# Task list info:

- name: <task-list-name>
- base_branch: <base-branch-name>

---

# Task list context: <optional>

Context to help understand the tasks. This section is optional.
This data is common to all tasks in the task list.

---

# Tasks

## Task 1. Brief description of the task

**Description**
Detailed description of the task

**Target directories**

- <directory-1> (<directory-description>)
- <directory-2> (<directory-description>)

**Status:** Pending|In Progress|Done|Canceled|Error

### Subtask 1.1. Brief description of the optional subtask

**Description**
Detailed description of the subtask

**Target directories**

- <directory-1> (<directory-description>)
- <directory-2> (<directory-description>)

**Status:** Pending|In Progress|Done|Canceled|Error
```

---

When working with the TASKS file, you will follow the instructions below:

- [tasks file] Find the next task with status "Pending" or "In Progress". This is the algorithm for finding the next task:
  ```
      for task in tasks:
          if task.status == "In Progress" or task.status == "Pending":
              if task.subtasks:
                  for subtask in task.subtasks:
                      if subtask.status == "In Progress" or subtask.status == "Pending":
                          return subtask
                  task.status = "Done"  # All subtasks are done, canceled or error
                  continue
              else:
                  return task
          else:
              continue
  ```
- [git] Create a branch for the task using the base branch defined in the task list info section as the base branch. The branch name must start with the task list name, followed by a hiphen, followed by the task number and followed by a hyphen and a descriptive name. Branch name example: `250729-1-property-address-required-fields`. Git command example: `git checkout -b <branch-name> <base-branch-name>`. Note: subtasks share the parent task branch.
- [tasks file] Mark its status as "In Progress" before starting to work on it.
- [execution] Execute the task (or subtask)
- [verification] If working in a bun module (like folders app, directus, mqtt-integration, protobuf), execute `bunx tsc --noEmit` inside the module directory to verify there are no types errors. Fix any errors before proceeding.
- [tasks file] Mark its status as "Done" when it is finished. If it is a subtask, and all subtasks are done, mark the parent task as "Done" too.
- Commit the changes with a message with the first line following the Conventional Commits format. For example: `chore: 250729-1-property-address-required-fields`. The rest of the message should be a brief description of what was done. Git command example: `git add -A && git commit -m "chore: 250729-1-property-address-required-fields" -m "Your description here"`
- [git] Merge the branch into the base branch. Git command example: `git checkout <base-branch-name> && git merge <branch-name>`

> NEVER PUSH A BRANCH TO THE REMOTE. Pushing a branch to the remote is not allowed.

> Make sure all changed files are saved before committing. Save all files before proceeding.

> Base branch is defined in the task list info section. If it is not defined, use `develop` as the base branch.

> Task list name can be found in the task list info section.

> Task status can be: Pending, In Progress, Done, Canceled and Error.

## Guidelines

When executing the task list, you will not ask anything to the user. You will just execute the tasks as defined in the TASKS file until all tasks are done. The user is not expected to interact with you during the execution.

[VERY IMPORTANT] When executing the task list, consult the Guidelines in subfolders of /docs/guidelines. See /docs/guidelines/00-INDEX.md for a list of all guidelines, this can help you find the relevant guidelines to understand the context of the task.

### Migration files

Refer to /docs/guidelines/backend/migrations-and-seed.md for more information on migration files.

### Seed script

Refer to /docs/guidelines/backend/migrations-and-seed.md for more information on migration files.

### Documentation updates

Any changes in the database structure must be documented. The documents that MUST be update are:

Refer to /docs/guidelines/backend/ddl-changes.md for more information on documentation updates.

## Frontend Changes

Refer to fronted guidelines in /docs/guidelines/frontend/ folder.

## Backend Changes

Changes in packages/directus and packages/mqtt-integration must comply with the guidelines in /docs/guidelines/backend/ folder.
