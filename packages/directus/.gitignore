# Directus Specific
data/
uploads/
extensions/.registry

# Dependencies
node_modules/

# General Logs
logs/
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment Variables
.env
.env.*
!.env.example
!.env.test
# Note: .env.*.local (from Next.js) is covered by .env.*

# Build Output (General)
# Framework specific build outputs (like .next/, .nuxt/, dist/, .output/, .svelte-kit/, build/) should be in their respective framework .gitignore files.

# Deployment Platforms
.netlify/
.vercel/
.wrangler/

# Testing Output
coverage/

# Cache / Temporary
.eslintcache
.stylelintcache

# Certificates
certificates/

# Operating System Files
.DS_Store
Thumbs.db

# IDE / Editor Configs
.idea
.vscode
!docker/mosquitto/data
docker/mosquitto/data/*
!docker/mosquitto/data/README.md
!docker/mosquitto/log
docker/mosquitto/log/*
!docker/mosquitto/log/README.md