import knexFactory, { K<PERSON> } from "knex";

/**
 * Create a Knex instance for tests using env:
 * TEST_DB_HOST, TEST_DB_PORT, TEST_DB_USER, TEST_DB_PASSWORD, TEST_DB_DATABASE
 */
export function createKnex(): Knex {
  const {
    TEST_DB_HOST,
    TEST_DB_PORT,
    TEST_DB_USER,
    TEST_DB_PASSWORD,
    TEST_DB_DATABASE,
  } = process.env;

  if (!TEST_DB_HOST || !TEST_DB_USER || !TEST_DB_DATABASE) {
    throw new Error(
      "Missing TEST DB env vars. Required: TEST_DB_HOST, TEST_DB_USER, TEST_DB_DATABASE. Optional: TEST_DB_PASSWORD, TEST_DB_PORT"
    );
  }

  const port =
    typeof TEST_DB_PORT === "string" ? parseInt(TEST_DB_PORT, 10) : 5432;

  return knexFactory({
    client: "pg",
    connection: {
      host: TEST_DB_HOST,
      port,
      user: TEST_DB_USER,
      password: TEST_DB_PASSWORD,
      database: TEST_DB_DATABASE,
    },
    pool: { min: 0, max: 5 },
  });
}

/**
 * Begin a transaction for a test. Must be rolled back by the caller.
 */
export async function begin(trxOrKnex?: Knex): Promise<Knex.Transaction> {
  const knex = trxOrKnex ?? createKnex();
  return knex.transaction();
}

/**
 * Rollback and destroy (safely) after a test.
 */
export async function rollbackAndDestroy(trx: Knex.Transaction): Promise<void> {
  try {
    await trx.rollback();
  } catch {
    // ignore
  }
  try {
    await (trx as unknown as Knex).destroy?.();
  } catch {
    // ignore
  }
}

/**
 * Executes a function within a database savepoint and expects it to reject/throw an error.
 * If the function succeeds unexpectedly, this helper will throw an error.
 * If the function fails as expected, the savepoint is rolled back while keeping the outer transaction intact.
 *
 * This utility is useful for testing error scenarios in database operations without affecting
 * the main transaction state.
 *
 * @param trx - The Knex transaction object to use for savepoint operations
 * @param fn - The async function that is expected to throw an error or reject
 *
 * @throws {Error} Throws an error if the provided function succeeds when it was expected to fail
 *
 * @example
 * ```typescript
 * await expectRejectWithinSavepoint(transaction, async () => {
 *   await someOperationThatShouldFail();
 * });
 * ```
 */
export async function expectRejectWithinSavepoint(
  trx: Knex,
  fn: () => Promise<any>
) {
  await trx.raw("SAVEPOINT sp_err");
  try {
    await fn();
    // If no error, release to keep the transaction clean, but assert fail
    await trx.raw("RELEASE SAVEPOINT sp_err");
    throw new Error("Expected statement to fail, but it succeeded");
  } catch (e) {
    // Rollback only the failed statement; keep outer transaction usable
    await trx.raw("ROLLBACK TO SAVEPOINT sp_err");
  }
}
