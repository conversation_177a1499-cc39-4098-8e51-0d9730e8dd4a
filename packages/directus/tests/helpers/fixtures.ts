import { K<PERSON> } from "knex";
import { randomUUID } from "crypto";

export async function insertUser(trx: Knex, overrides: Partial<any> = {}) {
  const id = overrides.id ?? randomUUID();
  const [row] = await trx("directus_users")
    .insert({
      id,
      first_name: overrides.first_name ?? "Test",
      last_name: overrides.last_name ?? "User",
      email:
        overrides.email ??
        `test_${Math.random().toString(36).slice(2)}@example.com`,
      password: overrides.password ?? "hash",
      status: overrides.status ?? "active",
      provider: overrides.provider ?? "default",
      role: overrides.role ?? null,
    })
    .returning(["id"]);
  return row.id ?? id;
}

export async function insertAccount(trx: Knex, owner: string) {
  const [row] = await trx("account").insert({ owner }).returning(["id"]);
  return row.id ?? row;
}

export async function insertProperty(
  trx: Knex,
  accountId: string,
  name = `Prop ${Math.random().toString(36).slice(2)}`
) {
  const [row] = await trx("property")
    .insert({
      account: accountId,
      name,
      timezone: "America/Sao_Paulo",
    })
    .returning(["id"]);
  return row.id ?? row;
}

export async function insertDevice(
  trx: Knex,
  model: "LIC" | "WPC-PL10" | "WPC-PL50" | "VC" | "RM",
  identifier?: string
) {
  const [row] = await trx("device")
    .insert({
      model,
      identifier:
        identifier ??
        Math.random().toString(16).slice(2) +
          Math.random().toString(16).slice(2),
    })
    .returning(["id"]);
  return row.id ?? row;
}

export async function insertPropertyDevice(
  trx: Knex,
  deviceId: string,
  propertyId: string,
  start: Date,
  end?: Date | null
) {
  const [row] = await trx("property_device")
    .insert({
      device: deviceId,
      property: propertyId,
      start_date: start,
      end_date: end ?? null,
    })
    .returning(["id"]);
  return row.id ?? row;
}

export async function insertMeshMapping(
  trx: Knex,
  meshPdId: string,
  licPdId: string,
  start: Date,
  end?: Date | null
) {
  const [row] = await trx("mesh_device_mapping")
    .insert({
      mesh_property_device: meshPdId,
      lic_property_device: licPdId,
      start_date: start,
      end_date: end ?? null,
    })
    .returning(["id"]);
  return row.id ?? row;
}

export async function getDeviceModel(trx: Knex, deviceId: string) {
  const row = await trx("device").where({ id: deviceId }).first("model");
  return row?.model as string | undefined;
}

export async function getCurrentMeshMappingId(
  trx: Knex,
  meshPdId: string
): Promise<string | null> {
  const row = await trx("property_device")
    .where({ id: meshPdId })
    .first("current_mesh_device_mapping");
  return (row?.current_mesh_device_mapping as string) ?? null;
}

export function daysAgo(n: number): Date {
  const d = new Date();
  d.setUTCDate(d.getUTCDate() - n);
  return d;
}

export function plusSeconds(date: Date, seconds: number): Date {
  const d = new Date(date.getTime());
  d.setUTCSeconds(d.getUTCSeconds() + seconds);
  return d;
}

export async function insertWaterPump(
  trx: Knex,
  propertyId: string,
  waterPumpController?: string | null,
  pumpType: "IRRIGATION" | "FERTIGATION" | "SERVICE" = "IRRIGATION",
  label: string = `Pump ${Math.random().toString(36).slice(2)}`,
  identifier: string = `PUMP_${Math.random()
    .toString(36)
    .slice(2)
    .toUpperCase()}`
) {
  const [row] = await trx("water_pump")
    .insert({
      property: propertyId,
      water_pump_controller: waterPumpController ?? null,
      pump_type: pumpType,
      label,
      identifier,
    })
    .returning(["id"]);
  return row.id ?? row;
}

export async function insertReservoir(
  trx: Knex,
  propertyId: string,
  name: string,
  reservoirMonitor?: string | null,
  waterPump?: string | null
) {
  const [row] = await trx("reservoir")
    .insert({
      property: propertyId,
      name,
      reservoir_monitor: reservoirMonitor ?? null,
      water_pump: waterPump ?? null,
    })
    .returning(["id"]);
  return row.id ?? row;
}

export async function insertProject(
  trx: Knex,
  propertyId: string,
  name: string,
  localizedIrrigationController?: string | null,
  irrigationWaterPump?: string | null,
  fertigationWaterPump?: string | null
) {
  const [row] = await trx("project")
    .insert({
      property: propertyId,
      name,
      localized_irrigation_controller: localizedIrrigationController ?? null,
      irrigation_water_pump: irrigationWaterPump ?? null,
      fertigation_water_pump: fertigationWaterPump ?? null,
    })
    .returning(["id"]);
  return row.id ?? row;
}

export async function insertSector(
  trx: Knex,
  projectId: string,
  name: string,
  valveController: string,
  valveControllerOutput: number = 1
) {
  const [row] = await trx("sector")
    .insert({
      project: projectId,
      name,
      valve_controller: valveController,
      valve_controller_output: valveControllerOutput,
    })
    .returning(["id"]);
  return row.id ?? row;
}
