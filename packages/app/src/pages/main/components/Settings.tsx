import {
  accountUser<PERSON>ree<PERSON>tom,
  authUser<PERSON>tom,
  availableAccounts<PERSON><PERSON>,
  logout<PERSON><PERSON>,
  selectedProperty<PERSON>tom,
} from "@/store";
import { useAtomValue, useSetAtom } from "jotai";
import {
  ArrowRightLeft,
  ContactRound,
  Edit2,
  KeyRound,
  Sprout,
  User,
} from "lucide-react";
import { useLocation } from "wouter";
import Button from "@/components/ui/Button";
import { APP_VERSION } from "@/utils/app";

function Settings() {
  const [, setLocation] = useLocation();
  const currentUser = useAtomValue(authUserAtom);
  const availableAccounts = useAtomValue(availableAccountsAtom);
  const currentProperty = useAtomValue(selectedPropertyAtom);
  const accountUserTree = useAtomValue(accountUserTreeAtom); // Assuming this is the correct atom for account user tree
  const currentAccount = accountUserTree?.account || null; // Get current account from the tree
  const logout = useSetAtom(logoutAtom);

  const features = {
    editProfile: false,
    changePassword: false,
    selectAccount: availableAccounts.length > 1, // Show if more than one account exists
    selectProperty: (currentAccount?.properties?.length || 0) > 1, // Show if more than one property exists
    pushNotifications: false,
    darkMode: false,
    about: true,
    logout: true,
  };

  // Defensive fallback for null user/account/property
  const userDisplay = currentUser
    ? {
        name:
          (currentUser.first_name || "") +
          (currentUser.last_name ? ` ${currentUser.last_name}` : ""),
        email: currentUser.email,
        avatar: currentUser.avatar,
      }
    : { name: "-", email: "-", avatar: null };
  const accountDisplay = currentAccount
    ? {
        name:
          currentAccount.owner &&
          ((currentAccount.owner.first_name || "") +
            (currentAccount.owner.last_name
              ? ` ${currentAccount.owner.last_name}`
              : "") ||
            currentAccount.owner.email ||
            "-"),
        role: accountUserTree?.role || "-",
      }
    : { name: "-", role: "-" };
  const propertyDisplay = currentProperty
    ? {
        name: currentProperty.name,
        projectCount: currentProperty.projects?.length || 0,
      }
    : { name: "-", projectCount: 0 };

  const handleLogout = async () => {
    await logout();
    setLocation("/login");
  };

  return (
    <SettingsContent
      currentUser={userDisplay}
      currentAccount={accountDisplay}
      currentProperty={propertyDisplay}
      setLocation={setLocation}
      handleLogout={handleLogout}
      features={features}
    />
  );
}

interface SettingsContentProps {
  currentUser: any;
  currentAccount: any;
  currentProperty: any;
  setLocation: (path: string) => void;
  handleLogout: () => void;
  features: {
    editProfile: boolean;
    changePassword: boolean;
    selectAccount: boolean;
    selectProperty: boolean;
    pushNotifications: boolean;
    darkMode: boolean;
    about: boolean;
    logout: boolean;
  };
}

function SettingsContent({
  currentUser,
  currentAccount,
  currentProperty,
  setLocation,
  handleLogout,
  features,
}: SettingsContentProps) {
  return (
    <div className="space-y-2">
      {/* User Profile Section (always visible, buttons optional) */}
      <div className="bg-white rounded-2xl shadow-sm border border-neutral-100 p-5">
        <div className="flex items-center space-x-4 mb-4">
          <div className="w-14 h-14 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center">
            <User className="w-7 h-7 text-primary-600" />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-base font-semibold text-neutral-900 truncate">
              {currentUser.name}
            </h3>
            <p className="text-sm text-neutral-500 truncate">
              {currentUser.email}
            </p>
          </div>
        </div>
        {(features.editProfile || features.changePassword) && (
          <div className="flex gap-2">
            {features.editProfile && (
              <Button
                size="sm"
                variant="primary"
                className="flex-1"
                icon={<Edit2 className="h-4 w-4" />}
              >
                Editar Perfil
              </Button>
            )}
            {features.changePassword && (
              <Button
                size="sm"
                variant="secondary"
                className="flex-1"
                icon={<KeyRound className="h-4 w-4" />}
              >
                Alterar Senha
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Account Section (selectAccount) */}
      {features.selectAccount && (
        <div className="bg-white rounded-2xl shadow-sm border border-neutral-100 overflow-hidden">
          <button
            onClick={() => setLocation("/select-account")}
            className="w-full flex items-center justify-between p-4 hover:bg-neutral-25 active:bg-neutral-50 transition-all duration-200 group"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                <ContactRound className="w-4 h-4 text-primary-500" />
              </div>
              <div className="text-left">
                <p className="text-xs font-medium text-neutral-600">
                  Conta selecionada
                </p>
                <p className="text-sm font-semibold text-neutral-900">
                  {currentAccount.name}
                </p>
                <p className="text-xs text-neutral-500">
                  {currentAccount.role}
                </p>
              </div>
            </div>
            <ArrowRightLeft className="w-4 h-4 text-neutral-400 group-hover:text-primary-500 transition-colors duration-200" />
          </button>
        </div>
      )}

      {/* Property Section (selectProperty) */}
      {features.selectProperty && (
        <div className="bg-white rounded-2xl shadow-sm border border-neutral-100 overflow-hidden">
          <button
            onClick={() => setLocation("/select-property")}
            className="w-full flex items-center justify-between p-4 hover:bg-neutral-25 active:bg-neutral-50 transition-all duration-200 group"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                <Sprout className="w-4 h-4 text-primary-500" />
              </div>
              <div className="text-left">
                <p className="text-xs font-medium text-neutral-600">
                  Propriedade selecionada
                </p>
                <p className="text-sm font-semibold text-neutral-900">
                  {currentProperty.name}
                </p>
                <p className="text-xs text-neutral-500">
                  {currentProperty.projectCount} projetos
                </p>
              </div>
            </div>
            <ArrowRightLeft className="w-4 h-4 text-neutral-400 group-hover:text-primary-500 transition-colors duration-200" />
          </button>
          {/* <div className="border-t border-neutral-100 p-3">
            <button
              onClick={() => setLocation("/app/projects")}
              className="w-full px-3 py-2 bg-primary-50 text-primary-700 text-xs font-medium rounded-lg hover:bg-primary-100 active:bg-primary-200 transition-all duration-200 flex items-center justify-center gap-1.5"
              aria-label="Gerenciar propriedade"
            >
              <Columns3Cog className="w-4 h-4 " />
              Gerenciar Propriedade
            </button>
          </div> */}
        </div>
      )}

      {/* App Settings (appConfig, pushNotifications, darkMode) */}
      {(features.pushNotifications || features.darkMode) && (
        <div className="bg-white rounded-2xl shadow-sm border border-neutral-100 p-4">
          <div className="space-y-3">
            {features.pushNotifications && (
              <div className="flex items-center justify-between py-1">
                <span className="text-sm font-medium text-neutral-700">
                  Notificações Push
                </span>
                <button className="relative inline-flex h-5 w-9 items-center rounded-full bg-primary-500 transition-colors">
                  <span className="inline-block h-3.5 w-3.5 transform rounded-full bg-white transition translate-x-5 shadow-sm" />
                </button>
              </div>
            )}
            {features.darkMode && (
              <div className="flex items-center justify-between py-1">
                <span className="text-sm font-medium text-neutral-700">
                  Modo Escuro
                </span>
                <button className="relative inline-flex h-5 w-9 items-center rounded-full bg-neutral-200 transition-colors">
                  <span className="inline-block h-3.5 w-3.5 transform rounded-full bg-white transition translate-x-0.5 shadow-sm" />
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* About Section (about) */}
      {features.about && (
        <div className="bg-white rounded-2xl shadow-sm border border-neutral-100 p-4">
          <div className="flex text-center justify-around ">
            <p className="text-sm font-semibold text-neutral-800">
              Irriga+ {APP_VERSION}
            </p>
            <p className="text-xs text-neutral-500">© 2025 ByAGRO</p>
          </div>
        </div>
      )}

      {/* Logout Button (logout) */}
      {features.logout && (
        <Button
          onClick={handleLogout}
          variant="destructive"
          size="md"
          className="w-full rounded-2xl"
        >
          Fazer Logout
        </Button>
      )}
    </div>
  );
}

export default Settings;
