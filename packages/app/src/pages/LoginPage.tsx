import LogoSymbol from "@/assets/logo-symbol-transparent-79x96.png"; // Adjust the import path as needed
import LogoWordmark from "@/assets/logo-wordmark-256x48.png"; // Adjust the import path as needed

import { useToast } from "@/components";
import Button from "@/components/ui/Button";
import { useAuth } from "@/store";
import React, { useState } from "react";

function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { showSuccess, showError } = useToast();

  const { login } = useAuth();
  // const { showSuccess, showError } = useToasts();

  const handleLogin = async () => {
    const result = await login({ email, password });
    if (result.success) {
    } else {
      showError({
        title: "Login Error",
        message: result.error || "Please check your credentials and try again.",
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await handleLogin();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="h-full overflow-y-auto px-4"
      style={{
        background:
          "linear-gradient(0deg, #18181c 0%, #121b23 40%, #042438 100%)",
      }}
    >
      <div className="w-full max-w-md mt-4">
        {/* Logo and Header */}
        <div className="text-center mb-4">
          <div className="mx-auto flex flex-col items-center text-center">
            <img
              src={LogoWordmark}
              alt="Irriga+ Wordmark"
              className="h-8 mb-2"
            />
          </div>
          <p className="text-gray-50">Sistema de Controle de Irrigação</p>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 relative">
          <img
            src={LogoSymbol}
            alt="Description"
            className="absolute top-4 right-4 h-16 object-cover"
          />
          <h2 className="text-2xl font-semibold text-gray-900 mb-4 text-center">
            Acesse sua conta
          </h2>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label
                className="block text-sm font-medium text-gray-700 mb-2"
                htmlFor="email"
              >
                Email
              </label>
              <input
                id="email"
                type="email"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
                placeholder="Digite seu email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                autoComplete="username"
              />
            </div>

            <div>
              <label
                className="block text-sm font-medium text-gray-700 mb-2"
                htmlFor="password"
              >
                Senha
              </label>
              <input
                id="password"
                type="password"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
                placeholder="Digite sua senha"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                autoComplete="current-password"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                />
                <span className="ml-2 text-sm text-gray-600">
                  Lembrar de mim
                </span>
              </label>
              <Button type="button" variant="ghost" className="text-sm">
                Esqueceu a senha?
              </Button>
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              variant="primary"
              className="w-full shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              loading={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Entrando...
                </div>
              ) : (
                "Entrar"
              )}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Não tem uma conta?{" "}
              <Button variant="ghost" className="font-medium">
                Solicite acesso
              </Button>
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-xs text-gray-500">
          <p>Sistema de Gestão de Irrigação Automatizada</p>
          <p className="mt-1">
            Para propriedades rurais de pequeno e médio porte
          </p>
        </div>
      </div>
    </div>
  );
}

export default LoginPage;
