import { useEffect } from "react";
import "./index.css";

import Routes from "./Routes";
import {
  PWAInstallBanner,
  NetworkStatus,
  LoadingOverlay,
  ErrorToast,
  ToastContainer,
} from "./components";
import { registerSW } from "./hooks/usePWA";
import { StoreProvider } from "./store/Provider";

export function App() {
  useEffect(() => {
    // Register service worker
    registerSW();
  }, []);

  return (
    <StoreProvider>
      <NetworkStatus />
      <Routes />
      <PWAInstallBanner />
      <LoadingOverlay />
      <ErrorToast />
      <ToastContainer />
    </StoreProvider>
  );
}

export default App;
