// src/store/data.ts
import { apiService } from "@/api";
import type {
  AccountUserTree,
  AccountUserWithAccount,
  AUTAccount,
  AUTDevice,
  AUTIrrigationPlan,
  AUTProject,
  AUTProperty,
  AUTPropertyDevice,
  AUTReservoir,
  AUTWaterPump,
} from "@/api/queries/account";
import type { CurrentProjectState } from "@/api/model/current-project-state";
import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { withOperationHandlingAtom } from "./operations";
import { TimeoutTimer } from "@/utils/timer";

// Debouncing configuration
export const ACCOUNT_USER_TREE_DEBOUNCE_DELAY = 100; // 100ms

// Account selection atoms
export const availableAccountsAtom = atom<AccountUserWithAccount[]>([]);
const internalSelectedAccountUserIdAtom = atomWithStorage<string | null>(
  "selectedAccountUserId",
  null,
  undefined,
  {
    getOnInit: true, // Load from storage on initialization
  }
);
export const selectedAccountUserIdAtom = atom(null, (get) => {
  const id = get(internalSelectedAccountUserIdAtom);
  if (!id) {
    console.warn("No account user ID selected");
  }
  return id;
});

export const accountUserTreeAtom = atom<AccountUserTree | null>(null);

// Selection atoms for navigation with persistence
export const selectedPropertyIdAtom = atomWithStorage<string | null>(
  "selectedPropertyId",
  null,
  undefined,
  {
    getOnInit: true, // Load from storage on initialization
  }
);

// Derived atoms for current selections
export const currentAccountAtom = atom<AUTAccount | null>((get) => {
  const tree = get(accountUserTreeAtom);
  return tree?.account || null;
});

export const propertiesAtom = atom<AUTProperty[]>((get) => {
  const account = get(currentAccountAtom);
  return account?.properties || [];
});

export const selectedPropertyAtom = atom<AUTProperty | null>((get) => {
  const properties = get(propertiesAtom);
  const selectedId = get(selectedPropertyIdAtom);
  return properties.find((p) => p.id === selectedId) || null;
});

export const projectsAtom = atom<AUTProject[]>((get) => {
  const property = get(selectedPropertyAtom);
  return property?.projects || [];
});

export const waterPumpsAtom = atom<AUTWaterPump[]>((get) => {
  const property = get(selectedPropertyAtom);
  return property?.water_pumps || [];
});

export const reservoirsAtom = atom<AUTReservoir[]>((get) => {
  const property = get(selectedPropertyAtom);
  return property?.reservoirs || [];
});

export const devicesAtom = atom<AUTDevice[]>((get) => {
  const property = get(selectedPropertyAtom);
  return property?.devices?.map((pd) => pd.device).filter(Boolean) || [];
});

export const licDevicesAtom = atom<AUTDevice[]>((get) => {
  const devices = get(devicesAtom);
  return (
    devices
      .filter((d) => d.model === "LIC")
      .map((d) => d)
      .filter(Boolean) || []
  );
});

// Enhanced devices atom that includes mesh device mapping information
export const propertyDevicesAtom = atom<AUTPropertyDevice[]>((get) => {
  const property = get(selectedPropertyAtom);
  return property?.devices || [];
});

// Device to LIC mapping atom - maps device IDs to their LIC device IDs
export const deviceToLicMapAtom = atom<Map<string, string | null>>((get) => {
  const propertyDevices = get(propertyDevicesAtom);
  const map = new Map<string, string | null>();

  propertyDevices.forEach((pd) => {
    if (pd.device?.id) {
      const licPropertyDeviceId =
        pd.current_mesh_device_mapping?.lic_property_device;

      // Find the LIC property_device using the ID
      let licDeviceId: string | null = null;
      if (licPropertyDeviceId) {
        const licPropertyDevice = propertyDevices.find((lpd) => {
          if (
            typeof licPropertyDeviceId === "object" &&
            licPropertyDeviceId !== null &&
            "id" in licPropertyDeviceId
          ) {
            return lpd.id === (licPropertyDeviceId as { id: string }).id;
          } else if (typeof licPropertyDeviceId === "string") {
            return lpd.id === licPropertyDeviceId;
          }
          return false;
        });

        // Get the device ID from the LIC property_device
        if (licPropertyDevice?.device?.id) {
          licDeviceId = licPropertyDevice.device.id;
        }
      }

      map.set(pd.device.id, licDeviceId);
    }
  });

  return map;
});

export const irrigationPlansAtom = atom<AUTIrrigationPlan[]>((get) => {
  const projects = get(projectsAtom);
  return projects.flatMap((project) => project.irrigation_plans || []);
});

// Property finder atom - finds a property by ID from current properties
export const propertyByIdAtom = atom(
  (get) =>
    (propertyId: string): AUTProperty | null => {
      const properties = get(propertiesAtom);
      return properties.find((p) => p.id === propertyId) || null;
    }
);

// Project finder atom - finds a project by ID from current projects
export const projectByIdAtom = atom(
  (get) =>
    (projectId: string): AUTProject | null => {
      const projects = get(projectsAtom);
      return projects.find((p) => p.id === projectId) || null;
    }
);

export const irrigationPlanByIdAtom = atom(
  (get) =>
    (planId: string): AUTIrrigationPlan | null => {
      const plans = get(irrigationPlansAtom);
      return plans.find((p) => p.id === planId) || null;
    }
);

export const devicesByIdAtom = atom(
  (get) =>
    (deviceId: string): AUTDevice | null => {
      const devices = get(devicesAtom);
      return devices.find((d) => d.id === deviceId) || null;
    }
);

export const waterPumpsByIdAtom = atom(
  (get) =>
    (pumpId: string): AUTWaterPump | null => {
      const pumps = get(waterPumpsAtom);
      return pumps.find((p) => p.id === pumpId) || null;
    }
);

export const reservoirsByIdAtom = atom(
  (get) =>
    (reservoirId: string): AUTReservoir | null => {
      const reservoirs = get(reservoirsAtom);
      return reservoirs.find((r) => r.id === reservoirId) || null;
    }
);

export const sectorsByProjectIdAtom = atom(
  (get) =>
    (projectId: string): AUTProject["sectors"] => {
      const projects = get(projectsAtom);
      const project = projects.find((p) => p.id === projectId);
      return (
        project?.sectors?.sort((a, b) => a.name.localeCompare(b.name)) || []
      );
    }
);

export const irrigationPlansByProjectIdAtom = atom(
  (get) =>
    (projectId: string): AUTIrrigationPlan[] => {
      const projectById = get(projectByIdAtom);
      const project = projectById(projectId);
      return (
        project?.irrigation_plans?.sort((a, b) =>
          a.start_time.localeCompare(b.start_time)
        ) || []
      );
    }
);

// Current project state atoms
export const currentProjectStatesAtom = atom<
  CurrentProjectState<{ project: string }>[]
>([]);
export const fetchCurrentProjectStatesAtom = atom(null, async (get, set) => {
  const selectedPropertyId = get(selectedPropertyIdAtom);
  if (!selectedPropertyId) {
    console.log("fetchCurrentProjectStatesAtom - No property ID selected");
    return;
  }

  console.log(
    `fetchCurrentProjectStatesAtom - Fetching current project states for property ${selectedPropertyId}`
  );
  // TODO: Implement minPacketDate
  const currentProjectStates =
    await apiService.currentProjectState.getByProperty(
      selectedPropertyId,
      null
    );
  console.log(
    `fetchCurrentProjectStatesAtom - Fetched current project states: ${JSON.stringify(
      currentProjectStates
    )}`
  );
  set(currentProjectStatesAtom, currentProjectStates);
});
fetchCurrentProjectStatesAtom.onMount = (dispatch) => {
  // dispatch();
  const timer = new TimeoutTimer();
  console.log("Starting timer");
  timer.start(5000, async () => {
    await dispatch();
  });
  return () => {
    console.log("Stopping timer");
    timer.stop();
  };
};

export const currentProjectStatesByPropertyAtom = atom<
  CurrentProjectState<{ project: string }>[]
>((get) => {
  const selectedPropertyId = get(selectedPropertyIdAtom);
  const currentProjectStates = get(currentProjectStatesAtom);

  if (!selectedPropertyId) {
    return [];
  }

  return currentProjectStates.filter((state) => {
    // Filter by property through project relationship
    const project = state.project;

    // If project is just an ID, we need to find it in the projects
    const projects = get(projectsAtom);
    const projectObj = projects.find((p) => p.id === project);
    return projectObj?.property === selectedPropertyId;
  });
});

export const currentProjectStateByProjectIdAtom = atom(
  (get) =>
    (projectId: string): CurrentProjectState<{ project: string }> | null => {
      const currentProjectStates = get(currentProjectStatesAtom);
      return (
        currentProjectStates.find((state) => {
          const project = state.project;
          return project === projectId;
        }) || null
      );
    }
);

// Loading and error states for current project states
export const currentProjectStatesLoadingAtom = atom<boolean>(false);
export const currentProjectStatesErrorAtom = atom<string | null>(null);

// Data fetching actions
export const fetchAccountsAtom = atom(null, async (get, set) => {
  const result = await set(
    withOperationHandlingAtom,
    {
      operation: "fetchAccounts",
      message: "Carregando contas...",
      displayLoading: true,
      displayError: true,
    },
    async () => {
      const accounts = await apiService.account.listAccountsWithUsers();
      set(availableAccountsAtom, accounts);
      return accounts;
    }
  );

  return result;
});

// Debouncing state for account user tree fetching
let fetchAccountUserTreeTimeout: NodeJS.Timeout | null = null;
let pendingAccountUserId: string | null = null;

// Helper function to clear the debounce timeout
const clearFetchAccountUserTreeTimeout = () => {
  if (fetchAccountUserTreeTimeout) {
    clearTimeout(fetchAccountUserTreeTimeout);
    fetchAccountUserTreeTimeout = null;
  }
  pendingAccountUserId = null;
};

const fetchSelectedAccountUserTreeAtom = atom(null, async (get, set) => {
  const targetId = get(internalSelectedAccountUserIdAtom);
  if (!targetId) {
    console.log(
      "fetchSelectedAccountUserTreeAtom - No account user ID provided"
    );
    throw new Error("No account user ID provided");
  }

  console.log(
    `fetchSelectedAccountUserTreeAtom - Requesting data for account ${targetId} (debounce: ${ACCOUNT_USER_TREE_DEBOUNCE_DELAY}ms)`
  );

  // If the same account ID is already pending, return early to avoid duplicate requests
  if (pendingAccountUserId === targetId && fetchAccountUserTreeTimeout) {
    console.log(
      `fetchSelectedAccountUserTreeAtom - Request for account ${targetId} already pending, skipping`
    );
    return new Promise((resolve) => {
      // Wait for the existing request to complete
      const checkInterval = setInterval(() => {
        if (!fetchAccountUserTreeTimeout || pendingAccountUserId !== targetId) {
          clearInterval(checkInterval);
          resolve({ success: true });
        }
      }, 50);
    });
  }

  // Clear any existing timeout to debounce the request
  clearFetchAccountUserTreeTimeout();
  pendingAccountUserId = targetId;

  // Return a promise that resolves after the debounce delay
  return new Promise((resolve, reject) => {
    fetchAccountUserTreeTimeout = setTimeout(async () => {
      try {
        // Double-check that the account ID hasn't changed during the debounce delay
        const currentTargetId = get(internalSelectedAccountUserIdAtom);
        if (currentTargetId !== targetId) {
          console.log(
            `fetchSelectedAccountUserTreeAtom - Account ID changed during debounce (${targetId} -> ${currentTargetId}), cancelling request`
          );
          clearFetchAccountUserTreeTimeout();
          resolve({ success: false, reason: "Account ID changed" });
          return;
        }

        console.log(
          `fetchSelectedAccountUserTreeAtom - Executing debounced request for account ${targetId}`
        );

        const result = await set(
          withOperationHandlingAtom,
          {
            operation: "fetchAccountUserTree",
            message: "Carregando dados da conta...",
            displayLoading: true,
            displayError: true,
          },
          async () => {
            const tree = await apiService.account.getAccountUserTree(targetId);
            set(accountUserTreeAtom, tree);
            console.log(
              `fetchSelectedAccountUserTreeAtom - Successfully loaded data for account ${targetId}`
            );
            Object.assign(window, {
              accountUserTree: tree, // For debugging purposes
            }); // For debugging purposes

            return tree;
          }
        );

        // Clear the pending state after successful completion
        clearFetchAccountUserTreeTimeout();
        resolve(result);
      } catch (error) {
        // Clear the pending state on error
        clearFetchAccountUserTreeTimeout();
        reject(error);
      }
    }, ACCOUNT_USER_TREE_DEBOUNCE_DELAY);
  });
});

// Helper atom to refetch data after mutations
export const refetchDataAtom = atom(null, async (get, set) => {
  const selectedAccountUserId = get(internalSelectedAccountUserIdAtom);
  if (selectedAccountUserId) {
    return await set(fetchSelectedAccountUserTreeAtom);
  }
  return { success: true };
});

// Helper atom to manually clear any pending debounced requests (useful for testing)
export const clearPendingAccountUserTreeRequestsAtom = atom(null, () => {
  clearFetchAccountUserTreeTimeout();
  console.log("🧹 Manually cleared pending account user tree requests");
});

// Fetch current project states for the selected property
// export const fetchCurrentProjectStatesByPropertyAtom = atom(
//   null,
//   async (get, set) => {
//     const selectedPropertyId = get(selectedPropertyIdAtom);

//     if (!selectedPropertyId) {
//       console.log(
//         "🚫 No property selected, skipping current project states fetch"
//       );
//       return [];
//     }

//     const result = await set(
//       withOperationHandlingAtom,
//       {
//         operation: "fetchCurrentProjectStates",
//         message: "Carregando estados dos projetos...",
//         displayLoading: false, // Don't show global loading for this
//         displayError: true,
//       },
//       async () => {
//         set(currentProjectStatesLoadingAtom, true);
//         set(currentProjectStatesErrorAtom, null);

//         try {
//           //TODO: Implement minPacketDate filtering
//           const projectStates =
//             await apiService.currentProjectState.getByProperty(
//               selectedPropertyId,
//               null
//             );
//           set(currentProjectStatesAtom, projectStates);
//           console.log(
//             `📊 Loaded ${projectStates.length} current project states for property ${selectedPropertyId}`
//           );
//           return projectStates;
//         } catch (error) {
//           const errorMessage =
//             error instanceof Error ? error.message : "Unknown error";
//           set(currentProjectStatesErrorAtom, errorMessage);
//           console.error("❌ Failed to fetch current project states:", error);
//           throw error;
//         } finally {
//           set(currentProjectStatesLoadingAtom, false);
//         }
//       }
//     );

//     return result;
//   }
// );

// Account assignment atom - handles account selection, data loading, and navigation
export const assignAccountAtom = atom(
  null,
  async (
    get,
    set,
    {
      accountUserId,
      navigate,
      nextLocation = "/app/dashboard",
    }: {
      accountUserId: string;
      nextLocation?: string;
      navigate: (path: string) => void;
    }
  ) => {
    // if (accountUserId === get(selectedAccountUserIdAtom)) {
    //   console.log("🔄 Already assigned to account:", accountUserId);
    //   // If already assigned, just navigate to the app dashboard
    //   navigate("/app/dashboard");
    //   return { success: true };
    // }
    try {
      console.log(
        "🔄 assignAccountAtom - Assigning current account:",
        accountUserId
      );

      // Step 1: Assign current account
      set(internalSelectedAccountUserIdAtom, accountUserId);

      // Step 2: Load current account data
      await set(fetchSelectedAccountUserTreeAtom);

      console.log(
        "✅ assignAccountAtom - Account data loaded successfully - redirecting to app"
      );

      // Step 3: Navigate to select property

      const selectedProperty = get(selectedPropertyAtom);

      if (selectedProperty) {
        // If a property is already selected, navigate to the app dashboard
        console.log(
          `🔄 assignAccountAtom - Property already selected: ${selectedProperty.id}, navigating to ${nextLocation}`
        );
        navigate(nextLocation);
        return { success: true };
      }

      // This will be handled by the existing route guards in AppShell
      console.log("🔄 No property selected - redirecting to select property");
      navigate("/select-property");

      return { success: true };
    } catch (error) {
      console.error("❌ Failed to load account data:", error);
      // On error, redirect to account selection
      navigate("/select-account");
      throw error;
    }
  }
);

export const unassignAccountAtom = atom(null, (get, set) => {
  console.log("🔄 Unassigning current account");
  // Clear any pending debounced fetch requests
  clearFetchAccountUserTreeTimeout();
  // Clear the selected account user ID
  set(internalSelectedAccountUserIdAtom, null);
  // Clear the account user tree
  set(accountUserTreeAtom, null);
  // Clear the selected property ID
  set(selectedPropertyIdAtom, null);
});
