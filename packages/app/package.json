{"name": "bun-react-template", "version": "1.0.0-b5", "private": true, "type": "module", "main": "src/index.tsx", "module": "src/index.tsx", "scripts": {"dev": "NODE_ENV=development bun run --watch src/index.tsx --console --hostname=0.0.0.0:3000", "start": "NODE_ENV=production bun src/index.tsx", "clean": "[ -d dist ] && rm -vr dist; exit 0", "prebuild": "bun run clean", "build": "bun run build.ts", "docker:build": "bun run build && docker build --rm -t docker-registry.saas.tecnologia.vix.br/byagro/irriga-mais-app:${npm_package_version} .", "docker:push": "docker push docker-registry.saas.tecnologia.vix.br/byagro/irriga-mais-app:${npm_package_version}", "docker:build-and-push": "bun run docker:build && bun run docker:push"}, "dependencies": {"@directus/sdk": "^20.0.0", "@szhsin/react-menu": "^4.4.1", "@types/geojson": "^7946.0.16", "@uidotdev/usehooks": "^2.4.1", "bun-plugin-tailwind": "0.0.15", "clsx": "^2.1.1", "dayjs": "^1.11.13", "jotai": "^2.12.5", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-tooltip": "^5.29.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "wouter": "^3.7.1", "codec-http-integration": "workspace:*"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/bun": "latest", "proto": "workspace:*"}}