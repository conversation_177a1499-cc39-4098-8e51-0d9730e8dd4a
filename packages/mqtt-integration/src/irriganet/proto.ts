import { codec } from "proto";
import {
  DEV_TYPES,
  MESH_DEVICE_TYPES,
  type IrriganetDeviceWithElementId,
  type LICState,
} from "./db-loader/types";

export function devicesPackage(
  state: LICState
): codec.in_.devices.DevicesPackage {
  // Map devices to DevicesData array, similar to how MainActivity.kt builds meshDevices
  const devicesData = state.devices
    .toSorted((a, b) => a.ord_idx - b.ord_idx)
    .map((device, index) => {
      // Find the corresponding mesh device
      const meshDevice = state.meshDevices.find(
        (md) => md.idx === device.mesh_idx
      );

      if (!meshDevice) {
        throw new Error(`Mesh device not found for device ${device.identity}`);
      }

      return codec.in_.devices.DevicesData.create({
        idx: device.ord_idx || index, // Device order index (ix in Android)
        mesh_id: meshDevice ? parseInt(meshDevice.identity, 16) : 0, // mesh_id (mi in Android) - hex to int
        device_id: parseInt(device.identity) || 0, // device_id (di in Android)
        device_type: device.type, // device_type (tp in Android)
        out1: device.out1 || 0, // out1 (o1 in Android)
        out2: device.out2 || 0, // out2 (o2 in Android)
        input: device.input || 0, // input (ip in Android)
        mode: device.mode || 0, // mode (md in Android)
        sector: device.sector || 0, // sector (sc in Android)
        group_idx: meshDevice.group_idx || 0, // group_idx (gp in Android)
        power: device.power || 0, // power (pw in Android)
        equipment: device.eqpt_ver || 0, // equipment (eq in Android)
      });
    });

  // Create and return the DevicesPackage
  return codec.in_.devices.DevicesPackage.create({
    data: devicesData,
  });
}

export function schedulingPackage(
  state: LICState,
  includeDeviceScheduling = true
): codec.in_.scheduling.SchedulingPackage {
  includeDeviceScheduling = includeDeviceScheduling !== false;
  // Map schedules to Scheduling array, similar to how MainActivity.kt builds scheduling
  const schedulingData = state.schedules
    .toSorted((a, b) => a.ord_idx - b.ord_idx)
    .map((schedule) => {
      return codec.in_.scheduling.Scheduling.create({
        idx: schedule.ord_idx || 0, // ord_idx -> idx (ix in Android)
        start_time: schedule.start_time, // start_time (st in Android)
        days_of_week: schedule.days_of_week, // days_of_week (dw in Android)
        number_of_steps: schedule.number_of_steps, // number_of_steps (ns in Android)
        allow_ferti: Boolean(schedule.allow_ferti), // allow_ferti (af in Android)
        allow_backwash: Boolean(schedule.allow_backwash), // allow_backwash (ab in Android)
        waterpump_idx: schedule.waterpump_ord_idx || 0, // waterpump_ord_idx (wi in Android)
        waterpump_working_time: schedule.waterpump_working_time || 0, // waterpump_working_time (wt in Android)
        ferti_idx: schedule.ferti_ord_idx || 0, // ferti_ord_idx (fi in Android)
        backwash_idx: schedule.backwash_ord_idx || 0, // backwash_ord_idx (bi in Android)
        group: schedule.group_idx, // group_idx (gi in Android) - find the group to get group index
        once: Boolean(schedule.once), // once (oc in Android) - não repetir o agendamento
      });
    });

  const schedulingPayload: codec.in_.scheduling.ISchedulingPackage = {
    type: includeDeviceScheduling
      ? codec.in_.scheduling.MsgType.MSG_SCHEDULING_ALL
      : codec.in_.scheduling.MsgType.MSG_SCHEDULING_ONLY,
    scheduling_data: schedulingData,
    device_scheduling_data: includeDeviceScheduling
      ? createDeviceSchedulingData(state)
      : undefined,
  };
  // Create and return the SchedulingPackage with only scheduling data
  return codec.in_.scheduling.SchedulingPackage.create(schedulingPayload);
}

export function deviceSchedulingPackage(
  state: LICState
): codec.in_.device_scheduling.DeviceSchedulingPackage {
  // Map device schedules to DeviceScheduling array
  const deviceSchedulingData = createDeviceSchedulingData(state);

  // Create and return the DeviceSchedulingPackage
  return codec.in_.device_scheduling.DeviceSchedulingPackage.create({
    data: deviceSchedulingData,
  });
}

function createDeviceSchedulingData(state: LICState) {
  return state.deviceSchedules
    .toSorted((a, b) => a.ord_idx - b.ord_idx)
    .map((deviceSchedule) => {
      const scheduling = state.schedules.find(
        (s) => s.idx === deviceSchedule.scheduling_idx
      );
      if (!scheduling) {
        throw new Error(
          `Scheduling not found for device schedule ${deviceSchedule.idx}`
        );
      }
      const device = state.devices.find(
        (d) => d.idx === deviceSchedule.device_idx
      );
      if (!device) {
        throw new Error(
          `Device not found for device schedule ${deviceSchedule.idx}`
        );
      }
      return codec.in_.device_scheduling.DeviceScheduling.create({
        idx: deviceSchedule.ord_idx || 0, // ord_idx -> idx (ix in Android)
        scheduling_idx: scheduling.ord_idx, // scheduling_idx (sh in Android) - references the scheduling
        device_idx: device.ord_idx, // device_idx (dx in Android) - references the device
        order: deviceSchedule.n_order, // n_order -> order (od in Android)
        sector_working_time: deviceSchedule.sector_working_time, // sector_working_time (st in Android)
        ferti_working_time: deviceSchedule.ferti_working_time || 0, // ferti_working_time (ft in Android)
        ferti_delay: deviceSchedule.ferti_delay,
      });
    });
}

export function automationPackage(
  state: LICState,
  magicValues = {
    mask: 6,
    value: 0,
  }
): codec.in_.automation.AutomationPackage {
  magicValues = magicValues || { mask: 6, value: 0 };
  const automationData: Array<codec.in_.automation.AutomationData> = [];
  state.meshDevices
    .filter((d) => d.type === MESH_DEVICE_TYPES.Level)
    .forEach((levelMeshDevice) => {
      const servicePumpMeshDevice = levelMeshDevice.level_pump_enable
        ? state.meshDevices.find(
            (dp) => dp.idx === levelMeshDevice.level_pump_idx
          )
        : null;
      const servicePumpDevice =
        servicePumpMeshDevice &&
        state.devices.find(
          (d) =>
            d.mesh_idx === servicePumpMeshDevice?.idx &&
            d.type === DEV_TYPES.ServicePump
        );
      const devices = state.devices.filter(
        (d) => d.mesh_idx === levelMeshDevice.idx
      );

      devices.forEach((device) => {
        automationData.push(
          codec.in_.automation.AutomationData.create({
            level_idx: device.ord_idx, // device_ord_idx -> level_idx (li in Android)
            pump_idx: servicePumpDevice ? servicePumpDevice.ord_idx : null, // service_pump_device_ord_idx -> pump_idx (pi in Android)
            mask: magicValues.mask,
            value: magicValues.value,
            working_time: levelMeshDevice.level_pump_working_time || 0, // level_working_time (lt in Android)
          })
        );
      });
    });
  return codec.in_.automation.AutomationPackage.create({
    data: automationData,
  });
}

export function configPackage(state: LICState): codec.in_.config.ConfigPackage {
  return codec.in_.config.ConfigPackage.create(state.lic.config);
}

export function request_infoPackage(
  _state: LICState
): codec.in_.request_info.RequestInfoPackage {
  return codec.in_.request_info.RequestInfoPackage.create({
    type: 0,
  });
}

type ControlPackageElementParams = {
  elementType: Extract<
    IrriganetDeviceWithElementId["elementType"],
    "valve" | "pump"
  >;
  elementId: string;
  elementVariation?: IrriganetDeviceWithElementId["elementVariation"];
} & (
  | {
      turnOn: false;
    }
  | {
      turnOn: true;
      durationMinutes: number;
    }
);

type ControlPackageDeviceParams = {
  /**
   * The ID of the device to control in the Irriga+ System.
   */
  device: string;
  /**
   * The deviceId in IrriganetDevice
   */
  deviceId: number;
} & (
  | {
      turnOn: false;
    }
  | {
      turnOn: true;
      durationMinutes: number;
    }
);

function findIrriganetDeviceByElementParams(
  state: LICState,
  params: ControlPackageElementParams
): IrriganetDeviceWithElementId {
  const devices = state.devices.filter(
    (d) =>
      d.elementType === params.elementType && d.elementId === params.elementId
  );
  if (!devices.length) {
    throw new Error("Device not found");
  }
  const device =
    devices.length > 1
      ? // Filter by variation
        devices.find(
          (d) =>
            d.elementVariation === params.elementVariation ||
            (!params.elementVariation && !d.elementVariation)
        )
      : devices[0];
  if (!device) {
    throw new Error("Device not found");
  }
  return device;
}

function findIrriganetDeviceByDeviceParams(
  state: LICState,
  params: ControlPackageDeviceParams
): IrriganetDeviceWithElementId {
  const meshDevice = state.meshDevices.find(
    (m) => m.deviceId === params.device
  );
  if (!meshDevice) {
    throw new Error("Mesh device not found");
  }
  const foundDevice = state.devices.find(
    (d) =>
      d.mesh_idx === meshDevice.idx && d.identity === String(params.deviceId)
  );
  if (!foundDevice) {
    throw new Error("Device not found");
  }
  return foundDevice;
}

export function controlPackage(
  state: LICState,
  params: ControlPackageElementParams | ControlPackageDeviceParams
): codec.in_.control.ControlPackage {
  const device =
    "device" in params
      ? findIrriganetDeviceByDeviceParams(state, params)
      : findIrriganetDeviceByElementParams(state, params);

  return codec.in_.control.ControlPackage.create({
    idx: device.ord_idx,
    action: params.turnOn
      ? codec.in_.control.MsgAction.MSG_TURN_ON
      : codec.in_.control.MsgAction.MSG_TURN_OFF,
    value: params.turnOn ? params.durationMinutes : 0,
  });
}

export function commandPacket(
  state: LICState,
  params:
    | {
        command: "resume";
      }
    | {
        command: "pause";
        durationMinutes: number;
      }
): codec.in_.command.CommandPackage {
  return codec.in_.command.CommandPackage.create({
    type:
      params.command === "resume"
        ? codec.in_.command.MsgType.MSG_RESUME
        : codec.in_.command.MsgType.MSG_PAUSE,
    value: params.command === "pause" ? params.durationMinutes : 0,
  });
}

export function firmware_updatePackage(
  state: LICState,
  params: codec.in_.firmware_update.IFirmwareUpdatePackage
): codec.in_.firmware_update.FirmwareUpdatePackage {
  return codec.in_.firmware_update.FirmwareUpdatePackage.create(params);
}
