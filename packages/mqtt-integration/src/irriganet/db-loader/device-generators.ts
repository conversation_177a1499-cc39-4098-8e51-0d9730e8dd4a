/**
 * Device Generation Module for IrrigaNet Database Loaders
 *
 * This module contains the main device generation logic that coordinates
 * the creation of mesh devices and individual devices from project data.
 * Following the Single Responsibility principle, this module orchestrates
 * the device generation process.
 */

import type {
  ProjectWithSchedulingData,
  ReservoirWithMonitorAndWaterPumpWithController,
  SectorWithValveController,
  WaterPumpWithController,
} from "../../db/queries/types";
import type {
  CodecConfig,
  IrriganetDeviceWithElementId,
  IrriganetGroup,
  IrriganetGroupWithProjectId,
  IrriganetMeshDeviceWithDeviceId,
  LICTree,
} from "./types";
import { IdGenerator, sortByDate } from "./utilities";
import { validateProjectPumpConfiguration } from "./validation";

import {
  addFertigationDevices,
  addIrrigationDevices,
  addLevelDevices,
  addServicePumpDevices,
  addValveDevices,
} from "./individual-device-generators";
import {
  createFertigationMeshDevice,
  createIrrigationMeshDevice,
  createLevelMeshDevice,
  createServiceMeshDevice,
  createValveMeshDevice,
} from "./mesh-device-generators";

// ==============================================
// MAIN DEVICE GENERATION
// ==============================================

/**
 * Generate all mesh devices and individual devices from LIC tree
 *
 * @param tree - Validated LIC tree
 * @param groups - Generated groups with project IDs
 * @param codec - Codec configuration
 * @param referenceDate - Reference date for processing
 * @returns Object containing mesh devices and individual devices
 */
export function generateMeshDevices(
  tree: LICTree,
  groups: Array<IrriganetGroupWithProjectId>,
  codec: CodecConfig,
  _referenceDate: Date
) {
  const result: {
    meshDevices: IrriganetMeshDeviceWithDeviceId[];
    devices: IrriganetDeviceWithElementId[];
  } = {
    meshDevices: [],
    devices: [],
  };
  const meshDeviceIdGen = new IdGenerator();
  const deviceIdGen = new IdGenerator();
  tree.projects.forEach((project) => {
    const group = groups.find((g) => g.projectId === project.id);
    if (!group) {
      throw new Error(`No group found for project ${project.id}`);
    }

    const { devices, meshDevices } = extractProjectDevices(
      codec,
      group,
      project,
      meshDeviceIdGen,
      deviceIdGen
    );

    result.meshDevices.push(...meshDevices);
    result.devices.push(...devices);
  });

  const extraGroup: IrriganetGroup = {
    idx: groups.reduce((maxIdx, group) => Math.max(maxIdx, group.idx), 0) + 1,
    name: "Extra Group",
  };

  const serviceResult = extractServicePumpsDevices(
    tree,
    codec,
    extraGroup,
    meshDeviceIdGen,
    deviceIdGen
  );

  result.meshDevices.push(...serviceResult.meshDevices);
  result.devices.push(...serviceResult.devices);

  const reservoirResult = extractReservoirsDevices(
    tree,
    codec,
    extraGroup,
    meshDeviceIdGen,
    deviceIdGen,
    result.meshDevices
  );

  result.meshDevices.push(...reservoirResult.meshDevices);
  result.devices.push(...reservoirResult.devices);

  return result;
}

// ==============================================
// PROJECT DEVICE EXTRACTION
// ==============================================

/**
 * Extract all devices from a single project
 *
 * @param codec - Codec configuration
 * @param group - Group associated with the project
 * @param project - Project data with scheduling information
 * @param meshDeviceIdGen - ID generator for mesh devices
 * @param deviceIdGen - ID generator for individual devices
 * @returns Object containing mesh devices and individual devices for the project
 */
export function extractProjectDevices(
  codec: CodecConfig,
  group: IrriganetGroupWithProjectId,
  project: ProjectWithSchedulingData,
  meshDeviceIdGen: IdGenerator,
  deviceIdGen: IdGenerator
) {
  // Validate project structure
  validateProjectPumpConfiguration(project);

  const meshDevices: Array<IrriganetMeshDeviceWithDeviceId> = [];
  const devices: Array<IrriganetDeviceWithElementId> = [];

  // Generate irrigation pump mesh device and devices
  const irrigationResult = generateIrrigationPumpDevices(
    codec,
    group,
    project,
    meshDeviceIdGen,
    deviceIdGen
  );
  meshDevices.push(...irrigationResult.meshDevices);
  devices.push(...irrigationResult.devices);

  // Generate fertigation pump mesh device and devices (if separate from irrigation)
  const fertigationResult = generateFertigationPumpDevices(
    codec,
    group,
    project,
    meshDeviceIdGen,
    deviceIdGen
  );
  meshDevices.push(...fertigationResult.meshDevices);
  devices.push(...fertigationResult.devices);

  // Generate valve mesh devices and devices
  const valveResult = generateValveDevices(
    codec,
    group,
    project,
    meshDeviceIdGen,
    deviceIdGen
  );
  meshDevices.push(...valveResult.meshDevices);
  devices.push(...valveResult.devices);

  return { meshDevices, devices };
}

function extractReservoirsDevices(
  tree: LICTree,
  codec: CodecConfig,
  extraGroup: IrriganetGroup,
  meshDeviceIdGen: IdGenerator,
  deviceIdGen: IdGenerator,
  currentMeshDevices: IrriganetMeshDeviceWithDeviceId[]
) {
  const meshDevices: IrriganetMeshDeviceWithDeviceId[] = [];
  const devices: IrriganetDeviceWithElementId[] = [];
  tree.reservoirs?.forEach((reservoir) => {
    const levelResult = generateLevelDevices(
      codec,
      extraGroup,
      meshDeviceIdGen,
      deviceIdGen,
      reservoir,
      currentMeshDevices
    );
    meshDevices.push(...levelResult.meshDevices);
    devices.push(...levelResult.devices);
  });
  return { meshDevices, devices };
}

function extractServicePumpsDevices(
  tree: LICTree,
  codec: CodecConfig,
  extraGroup: IrriganetGroup,
  meshDeviceIdGen: IdGenerator,
  deviceIdGen: IdGenerator
) {
  const meshDevices: IrriganetMeshDeviceWithDeviceId[] = [];
  const devices: IrriganetDeviceWithElementId[] = [];
  tree.servicePumps?.forEach((servicePump) => {
    const serviceResult = generateServicePumpDevices(
      codec,
      extraGroup,
      meshDeviceIdGen,
      deviceIdGen,
      servicePump
    );
    meshDevices.push(...serviceResult.meshDevices);
    devices.push(...serviceResult.devices);
  });
  return { meshDevices, devices };
}

// ==============================================
// IRRIGATION PUMP DEVICE GENERATION
// ==============================================

/**
 * Generate irrigation pump mesh device and associated individual devices
 */
function generateIrrigationPumpDevices(
  codec: CodecConfig,
  group: IrriganetGroupWithProjectId,
  project: ProjectWithSchedulingData,
  meshDeviceIdGen: IdGenerator,
  deviceIdGen: IdGenerator
) {
  const meshDevices: IrriganetMeshDeviceWithDeviceId[] = [];
  const devices: IrriganetDeviceWithElementId[] = [];

  const irrigationPump = project.irrigation_water_pump;
  const pumpController = irrigationPump.water_pump_controller;

  // Check if fertigation and irrigation controllers are shared
  const fertigationAndIrrigationControllerShared =
    project.fertigation_water_pump?.water_pump_controller?.id ===
    pumpController.id;

  // Create irrigation mesh device
  const pumpMeshDevice = createIrrigationMeshDevice(
    meshDeviceIdGen,
    codec,
    group,
    project,
    fertigationAndIrrigationControllerShared
  );
  meshDevices.push(pumpMeshDevice);

  // Add individual devices to the mesh device
  addIrrigationDevices(
    deviceIdGen,
    devices,
    pumpMeshDevice,
    project,
    fertigationAndIrrigationControllerShared
  );

  return { meshDevices, devices };
}

// ==============================================
// FERTIGATION PUMP DEVICE GENERATION
// ==============================================

/**
 * Generate fertigation pump mesh device and associated individual devices
 * (only if separate from irrigation controller)
 */
function generateFertigationPumpDevices(
  codec: CodecConfig,
  group: IrriganetGroupWithProjectId,
  project: ProjectWithSchedulingData,
  meshDeviceIdGen: IdGenerator,
  deviceIdGen: IdGenerator
) {
  const meshDevices: IrriganetMeshDeviceWithDeviceId[] = [];
  const devices: IrriganetDeviceWithElementId[] = [];

  // Only create separate fertigation mesh device if not shared with irrigation
  const fertigationPump = project.fertigation_water_pump;
  if (!fertigationPump?.water_pump_controller) {
    return { meshDevices, devices };
  }

  const irrigationPumpController =
    project.irrigation_water_pump.water_pump_controller;
  const fertigationAndIrrigationControllerShared =
    fertigationPump.water_pump_controller.id === irrigationPumpController.id;

  if (fertigationAndIrrigationControllerShared) {
    return { meshDevices, devices };
  }

  // Create fertigation mesh device
  const fertiMeshDevice = createFertigationMeshDevice(
    meshDeviceIdGen,
    codec,
    group,
    project
  );
  meshDevices.push(fertiMeshDevice);

  // Add individual devices to the mesh device
  addFertigationDevices(deviceIdGen, devices, fertiMeshDevice, project);

  return { meshDevices, devices };
}

// ==============================================
// SERVICE PUMP DEVICE GENERATION
// ==============================================

/**
 * Generate service pump mesh device and associated individual devices
 */
function generateServicePumpDevices(
  codec: CodecConfig,
  group: IrriganetGroup,
  meshDeviceIdGen: IdGenerator,
  deviceIdGen: IdGenerator,
  servicePump: WaterPumpWithController
) {
  const meshDevices: IrriganetMeshDeviceWithDeviceId[] = [];
  const devices: IrriganetDeviceWithElementId[] = [];

  // Create service mesh device
  const pumpMeshDevice = createServiceMeshDevice(
    meshDeviceIdGen,
    codec,
    group,
    servicePump
  );
  meshDevices.push(pumpMeshDevice);

  // Add individual devices to the mesh device
  addServicePumpDevices(deviceIdGen, devices, pumpMeshDevice, servicePump);

  return { meshDevices, devices };
}

// ==============================================
// VALVE DEVICE GENERATION
// ==============================================

/**
 * Generate valve mesh devices and associated individual devices
 */
function generateValveDevices(
  codec: CodecConfig,
  group: IrriganetGroupWithProjectId,
  project: ProjectWithSchedulingData,
  meshDeviceIdGen: IdGenerator,
  deviceIdGen: IdGenerator
) {
  const meshDevices: IrriganetMeshDeviceWithDeviceId[] = [];
  const devices: IrriganetDeviceWithElementId[] = [];

  // Group sectors by valve controller
  const groupedSectors = groupSectorsByValveController(project.sectors);

  // Process each valve controller group
  Object.values(groupedSectors)
    .filter((sectors) => sectors && sectors.length > 0)
    .forEach((sectors) => {
      const sortedSectors = sortSectorsByCreationDate(sectors!);

      // Create mesh device for the valve controller
      const meshDevice = createValveMeshDevice(
        meshDeviceIdGen,
        codec,
        group,
        sortedSectors[0]!
      );
      meshDevices.push(meshDevice);

      // Add individual valve devices
      addValveDevices(
        deviceIdGen,
        devices,
        meshDevice,
        project,
        sortSectorsByName(sortedSectors)
      );
    });

  return { meshDevices, devices };
}

// ==============================================
// LEVEL DEVICE GENERATION
// ==============================================

/**
 * Generate level mesh device and associated individual devices
 */
function generateLevelDevices(
  codec: CodecConfig,
  group: IrriganetGroup,
  meshDeviceIdGen: IdGenerator,
  deviceIdGen: IdGenerator,
  reservoir: ReservoirWithMonitorAndWaterPumpWithController,
  currentMeshDevices: IrriganetMeshDeviceWithDeviceId[]
) {
  const meshDevices: IrriganetMeshDeviceWithDeviceId[] = [];
  const devices: IrriganetDeviceWithElementId[] = [];

  // Create reservoir mesh device
  const reservoirMeshDevice = createLevelMeshDevice(
    meshDeviceIdGen,
    codec,
    group,
    reservoir,
    currentMeshDevices
  );
  meshDevices.push(reservoirMeshDevice);

  // Add individual devices to the mesh device
  addLevelDevices(deviceIdGen, devices, reservoirMeshDevice, reservoir);

  return { meshDevices, devices };
}

// ==============================================
// SECTOR GROUPING AND SORTING UTILITIES
// ==============================================

/**
 * Group sectors by their valve controller
 */
function groupSectorsByValveController(
  sectors: SectorWithValveController[]
): Record<string, SectorWithValveController[] | undefined> {
  return Object.groupBy(sectors, (sector) => sector.valve_controller);
}

/**
 * Sort sectors by valve controller creation date
 */
function sortSectorsByCreationDate(
  sectors: SectorWithValveController[]
): SectorWithValveController[] {
  return sortByDate(
    sectors,
    (sector) => sector.valve_controller_device.date_created
  );
}

/**
 * Sort sectors by name
 */
function sortSectorsByName(
  sectors: SectorWithValveController[]
): SectorWithValveController[] {
  return sectors.toSorted((a, b) => a.name.localeCompare(b.name));
}
