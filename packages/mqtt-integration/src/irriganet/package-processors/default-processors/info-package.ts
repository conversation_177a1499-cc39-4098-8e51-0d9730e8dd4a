import { updateLICStateFromInfoPackage } from "@/db/mutations/lic-state";
import { <PERSON>ggerManager } from "@/log";
import type { DefaultPackageProcessorFunction } from "../types";

const log = LoggerManager.getLogger("InfoPackageProcessor");

export const processInfoPackage: DefaultPackageProcessorFunction<
  "info"
> = async (id, infoData, ctx) => {
  if (log.debugEnabled) {
    log.debug(
      `[${ctx.state.lic.identity}] Processing InfoPackage for sync tracking:`,
      infoData
    );
  }

  // Extract timestamp fields from InfoPackage
  const infoPackage: {
    devices_id?: number;
    scheduling_id?: number;
    dev_scheduling_id?: number;
    automation_id?: number;
    config_id?: number;
  } = {};

  if (infoData.devices_id !== undefined) {
    infoPackage.devices_id = Number(infoData.devices_id);
  }
  if (infoData.scheduling_id !== undefined) {
    infoPackage.scheduling_id = Number(infoData.scheduling_id);
  }
  if (infoData.dev_scheduling_id !== undefined) {
    infoPackage.dev_scheduling_id = Number(infoData.dev_scheduling_id);
  }
  if (infoData.automation_id !== undefined) {
    infoPackage.automation_id = Number(infoData.automation_id);
  }
  if (infoData.config_id !== undefined) {
    infoPackage.config_id = Number(infoData.config_id);
  }

  // Update lic_state with current timestamps
  await updateLICStateFromInfoPackage(
    ctx.db,
    ctx.state.lic.irrigaMaisDeviceId,
    infoPackage
  )
    .then(() => {
      if (log.debugEnabled) {
        log.debug(
          `[${ctx.state.lic.identity}] Updated lic_state with InfoPackage timestamps`
        );
      }
      return ctx.codecManager.checkOutdatedConfiguration();
    })
    .catch((error) => {
      log.error(
        `[${ctx.state.lic.identity}] Failed to update lic_state with InfoPackage:`,
        error
      );
    });
};
