import type {
  CurrentLICPacket,
  DeviceMessageRequest,
  LICStateRecord,
  CurrentProjectState,
} from "../queries/types";

export type CurrentLICPacketInsert = Omit<
  CurrentLICPacket,
  "id" | "date_created"
>;

export type DeviceMessageRequestInsert = Omit<
  DeviceMessageRequest,
  "id" | "date_created" | "date_updated" | "user_created" | "user_updated"
>;

export type DeviceMessageRequestUpdate = Partial<
  Pick<
    DeviceMessageRequest,
    | "status"
    | "attempts"
    | "payload_bytes"
    | "sent_at"
    | "acknowledged_at"
    | "last_error"
    | "metadata"
    | "notes"
  >
>;

export type LICStateInsert = Omit<
  LICStateRecord,
  "date_created" | "date_updated" | "user_created" | "user_updated"
>;

export type LICStateUpdate = Partial<
  Pick<
    LICStateRecord,
    | "lic"
    | "groups"
    | "devices"
    | "mesh_devices"
    | "schedules"
    | "sector_schedules"
    | "device_schedules"
    | "last_devices_request"
    | "last_scheduling_request"
    | "last_dev_scheduling_request"
    | "last_automation_request"
    | "last_config_request"
    | "current_devices_timestamp"
    | "current_scheduling_timestamp"
    | "current_dev_scheduling_timestamp"
    | "current_automation_timestamp"
    | "current_config_timestamp"
  >
>;

export type CurrentProjectStateInsert = Omit<
  CurrentProjectState,
  "id" | "date_created" | "date_updated"
>;

export type CurrentProjectStateUpdate = Partial<
  Pick<
    CurrentProjectState,
    "irrigation_status" | "fertigation_status" | "backwash_status" | "sectors"
  >
>;
