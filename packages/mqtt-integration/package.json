{"name": "mqtt-integration", "version": "1.0.0-b6", "description": "MQTT Integration Package", "module": "index.ts", "type": "module", "private": true, "scripts": {"dev": "bun run --watch src/index.ts", "build": "bun build src/index.ts --outdir dist --target bun", "build:deps": "cd ../protobuf && bun run build", "type-check": "tsc --noEmit", "docker:build": "cd ../..; docker build --rm --target mqtt-integration -t docker-registry.saas.tecnologia.vix.br/byagro/irriga-mais/mqtt-integration:${npm_package_version} -f Dockerfile .", "docker:push": "docker push docker-registry.saas.tecnologia.vix.br/byagro/irriga-mais/mqtt-integration:${npm_package_version}", "docker:build-and-push": "bun run docker:build && bun run docker:push"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"fastq": "^1.19.1", "mqtt": "^5.13.3", "postgres": "^3.4.7", "proto": "workspace:*"}}