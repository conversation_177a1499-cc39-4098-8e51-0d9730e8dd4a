const USERNAME = "byagro";
const PASSWORD = "i8dEYH7tcNxVf18";

function getAuthHeader(): string {
  const credentials = `${USERNAME}:${PASSWORD}`;
  if (typeof Buffer !== "undefined" && typeof Buffer.from === "function") {
    return "Basic " + Buffer.from(credentials, "utf8").toString("base64");
  }
  if (typeof btoa === "function") {
    // handle UTF-8 safely in browsers
    return "Basic " + btoa(unescape(encodeURIComponent(credentials)));
  }
  throw new Error("No base64 encoding available in this environment");
}

function ipToInt(ip: string): number {
  const parts = ip.trim().split(".");
  if (parts.length !== 4) throw new Error(`Invalid IPv4 address: ${ip}`);
  return (
    parts.reduce((acc, part) => {
      const n = Number(part);
      if (!Number.isInteger(n) || n < 0 || n > 255)
        throw new Error(`Invalid IPv4 octet: ${part}`);
      return ((acc << 8) >>> 0) + n;
    }, 0) >>> 0
  );
}

function intToIp(num: number): string {
  return [
    (num >>> 24) & 0xff,
    (num >>> 16) & 0xff,
    (num >>> 8) & 0xff,
    num & 0xff,
  ].join(".");
}

/**
 * Generator that yields every IPv4 address for the given mask.
 * - If ipMask is a CIDR (e.g. "***********/24") it yields all addresses from network to broadcast (inclusive).
 * - If ipMask has no "/" it yields the single IP (e.g. "***********").
 */
function* iterateIpMask(ipMask: string): Generator<string> {
  const cidr = ipMask.trim();
  const m = cidr.match(/^(.+)\/(\d{1,2})$/);
  if (!m) {
    // single IP
    yield cidr;
    return;
  }

  const baseIp = m[1];
  const prefix = Number(m[2]);
  if (!Number.isInteger(prefix) || prefix < 0 || prefix > 32) {
    throw new Error(`Invalid CIDR prefix: ${m[2]}`);
  }

  const baseInt = ipToInt(baseIp);
  const mask = prefix === 0 ? 0 : (0xffffffff << (32 - prefix)) >>> 0;
  const network = baseInt & mask;
  const broadcast = (network | (~mask >>> 0)) >>> 0;

  let cur = network >>> 0;
  while (true) {
    yield intToIp(cur);
    if (cur === broadcast) break;
    cur = (cur + 1) >>> 0;
  }
}

function requestCodecInfo(ip: string) {
  const url = `http://${ip}/report`;
  const headers = {
    Authorization: getAuthHeader(),
    "Content-Type": "application/x-protobuf",
  };

  return fetch(url, {
    method: "POST",
    headers,
    body: new Uint8Array([2]),
  }).then((response) => {
    if (!response.ok)
      throw new Error(
        `Failed to fetch codec info from ${ip}: ${response.status} - ${
          response.statusText
        } - ${JSON.stringify(response.headers)}`
      );
    return response.text();
  });
}

/**
 * A queue of IP addresses to scan.
 */
const queue: string[] = [];

function processQueue(
  concurrency: number,
  cb: (
    ip: string,
    result: { success: true; data: any } | { success: false; error: Error }
  ) => void
) {
  // Process the queue with the given concurrency
  const activeRequests: Promise<void>[] = [];
  for (let i = 0; i < concurrency; i++) {
    activeRequests.push(processNext());
  }

  function processNext(): Promise<void> {
    if (queue.length === 0) return Promise.resolve();

    const ip = queue.shift();
    if (!ip) return Promise.resolve();

    return requestCodecInfo(ip)
      .then((response) => {
        cb(ip, { success: true, data: response });
      })
      .catch((error) => {
        cb(ip, { success: false, error });
      })
      .finally(() => {
        console.log(
          "Queue length:",
          queue.length,
          "active requests:",
          activeRequests.length
        );
        return processNext();
      });
  }

  return Promise.all(activeRequests);
}

/**
 * Scans a range of IP addresses for codec http info discovery and calls the callback with the results.
 * Performs a GET request on the http address http://<ip>/info to check for codec information.
 * @param ipMask The IP address range to scan (e.g., "***********/24").
 * @param cb The callback to call with each discovered IP address and its response.
 */
function scan(
  ipMask: string,
  cb: (
    ip: string,
    result: { success: true; data: any } | { success: false; error: Error }
  ) => void
) {
  for (const ip of iterateIpMask(ipMask)) {
    queue.push(ip);
  }

  // Process the queue with concurrency 10
  processQueue(10, cb);
}

scan("***********/24", (ip, result) => {
  if (result.success) {
    console.log(`[${ip}]:`, result.data);
    Bun.file(`${ip.replaceAll(".", "_")}.json`).write(result.data);
  } else {
    console.error(`[${ip}]:`, result.error.message);
  }
});

// requestCodecInfo("*************")
//   .then((data) => {
//     console.log(data);
//   })
//   .catch((error) => {
//     console.error(error);
//   });
