[***********]: Failed to fetch codec info from ***********: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:08:32 GMT","content-type":"text/html","transfer-encoding":"chunked","connection":"keep-alive","content-encoding":"gzip","server":"nginx"}
Queue length: 246 active requests: 10
[***********]: Failed to fetch codec info from ***********: 404 - Not found - {"content-type":"text/html"}
Queue length: 245 active requests: 10
[***********]: ����6
90150667FCF4{e n(
8@�˽�H�ͽ�P�˽�X�̽�`�˽�
Queue length: 244 active requests: 10
[************]: Failed to fetch codec info from ************: 404 - Not Found - {"cache-control":"no-cache","content-length":"604","content-type":"text/html","connection":"close","pragma":"no-cache","server":"debut/1.20"}
Queue length: 243 active requests: 10
[************]: Unable to connect. Is the computer able to access the url?
Queue length: 242 active requests: 10
[************]: Unable to connect. Is the computer able to access the url?
Queue length: 241 active requests: 10
[***********]: Unable to connect. Is the computer able to access the url?
Queue length: 240 active requests: 10
[***********]: Unable to connect. Is the computer able to access the url?
Queue length: 239 active requests: 10
[192.168.2.5]: Unable to connect. Is the computer able to access the url?
Queue length: 238 active requests: 10
[192.168.2.3]: Unable to connect. Is the computer able to access the url?
Queue length: 237 active requests: 10
[192.168.2.1]: Unable to connect. Is the computer able to access the url?
Queue length: 236 active requests: 10
[192.168.2.0]: Unable to connect. Is the computer able to access the url?
Queue length: 235 active requests: 10
[192.168.2.19]: Unable to connect. Is the computer able to access the url?
Queue length: 234 active requests: 10
[***********1]: Unable to connect. Is the computer able to access the url?
Queue length: 233 active requests: 10
[192.168.2.17]: <methodResponse><fault><value><struct><member><name>faultCode</name><value><i4>1</i4></value></member><member><name>faultString</name><value>Failed to parse supplied XML</value></member></struct></value></fault></methodResponse>
Queue length: 232 active requests: 10
[***********2]: Unable to connect. Is the computer able to access the url?
Queue length: 231 active requests: 10
[192.168.2.15]: Unable to connect. Is the computer able to access the url?
Queue length: 230 active requests: 10
[192.168.2.11]: Unable to connect. Is the computer able to access the url?
Queue length: 229 active requests: 10
[***********4]: Unable to connect. Is the computer able to access the url?
Queue length: 228 active requests: 10
[***********3]: Failed to fetch codec info from ***********3: 404 - Not Found - {"content-length":"49","content-type":"text/html"}
Queue length: 227 active requests: 10
[192.168.2.14]: Unable to connect. Is the computer able to access the url?
Queue length: 226 active requests: 10
[***********5]: Unable to connect. Is the computer able to access the url?
Queue length: 225 active requests: 10
[***********0]: Unable to connect. Is the computer able to access the url?
Queue length: 224 active requests: 10
[192.168.2.18]: Unable to connect. Is the computer able to access the url?
Queue length: 223 active requests: 10
[***********6]: Unable to connect. Is the computer able to access the url?
Queue length: 222 active requests: 10
[***********7]: Unable to connect. Is the computer able to access the url?
Queue length: 221 active requests: 10
[***********9]: Unable to connect. Is the computer able to access the url?
Queue length: 220 active requests: 10
[192.168.2.36]: Failed to fetch codec info from 192.168.2.36: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:08:38 GMT","content-type":"text/html","transfer-encoding":"chunked","connection":"keep-alive","x-frame-options":"SAMEORIGIN","server":"nginx"}
Queue length: 219 active requests: 10
[***********8]: Unable to connect. Is the computer able to access the url?
Queue length: 218 active requests: 10
[192.168.2.30]: Unable to connect. Is the computer able to access the url?
Queue length: 217 active requests: 10
[192.168.2.39]: Failed to fetch codec info from 192.168.2.39: 401 - Unauthorized - {"connection":"close","www-authenticate":"Basic realm=\"Controladora de Acesso\""}
Queue length: 216 active requests: 10
[192.168.2.33]: Unable to connect. Is the computer able to access the url?
Queue length: 215 active requests: 10
[192.168.2.32]: Unable to connect. Is the computer able to access the url?
Queue length: 214 active requests: 10
[192.168.2.31]: Unable to connect. Is the computer able to access the url?
Queue length: 213 active requests: 10
[***********1]: Unable to connect. Is the computer able to access the url?
Queue length: 212 active requests: 10
[192.168.2.34]: Unable to connect. Is the computer able to access the url?
Queue length: 211 active requests: 10
[192.168.2.35]: Unable to connect. Is the computer able to access the url?
Queue length: 210 active requests: 10
[192.168.2.37]: Unable to connect. Is the computer able to access the url?
Queue length: 209 active requests: 10
[192.168.2.38]: Unable to connect. Is the computer able to access the url?
Queue length: 208 active requests: 10
[***********0]: Unable to connect. Is the computer able to access the url?
Queue length: 207 active requests: 10
[***********4]: Unable to connect. Is the computer able to access the url?
Queue length: 206 active requests: 10
[***********3]: Unable to connect. Is the computer able to access the url?
Queue length: 205 active requests: 10
[***********2]: Unable to connect. Is the computer able to access the url?
Queue length: 204 active requests: 10
[192.168.2.50]: Unable to connect. Is the computer able to access the url?
Queue length: 203 active requests: 10
[***********5]: Unable to connect. Is the computer able to access the url?
Queue length: 202 active requests: 10
[***********6]: Unable to connect. Is the computer able to access the url?
Queue length: 201 active requests: 10
[192.168.2.54]: Failed to fetch codec info from 192.168.2.54: 404 - Not Found - {"content-length":"49","content-type":"text/html"}
Queue length: 200 active requests: 10
[192.168.2.56]: Unable to connect. Is the computer able to access the url?
Queue length: 199 active requests: 10
[***********7]: Unable to connect. Is the computer able to access the url?
Queue length: 198 active requests: 10
[192.168.2.58]: Failed to fetch codec info from 192.168.2.58: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:08:44 GMT","content-length":"0","server":"Microsoft-IIS/10.0"}
Queue length: 197 active requests: 10
[***********8]: Unable to connect. Is the computer able to access the url?
Queue length: 196 active requests: 10
[***********9]: Unable to connect. Is the computer able to access the url?
Queue length: 195 active requests: 10
[192.168.2.53]: Unable to connect. Is the computer able to access the url?
Queue length: 194 active requests: 10
[192.168.2.52]: Unable to connect. Is the computer able to access the url?
Queue length: 193 active requests: 10
[192.168.2.51]: Unable to connect. Is the computer able to access the url?
Queue length: 192 active requests: 10
[192.168.2.55]: Unable to connect. Is the computer able to access the url?
Queue length: 191 active requests: 10
[192.168.2.57]: Unable to connect. Is the computer able to access the url?
Queue length: 190 active requests: 10
[***********6]: Failed to fetch codec info from ***********6: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:08:47 GMT","content-length":"319","keep-alive":"timeout=15, max=100","connection":"Keep-Alive","content-type":"text/html; charset=iso-8859-1","server":"Apache/2.2.9 (Debian) PHP/5.2.6-1+lenny9 with Suhosin-Patch"}
Queue length: 189 active requests: 10
[192.168.2.59]: Unable to connect. Is the computer able to access the url?
Queue length: 188 active requests: 10
[***********8]: Unable to connect. Is the computer able to access the url?
Queue length: 187 active requests: 10
[***********0]: Unable to connect. Is the computer able to access the url?
Queue length: 186 active requests: 10
[***********0]: Unable to connect. Is the computer able to access the url?
Queue length: 185 active requests: 10
[***********1]: Unable to connect. Is the computer able to access the url?
Queue length: 184 active requests: 10
[***********4]: Unable to connect. Is the computer able to access the url?
Queue length: 183 active requests: 10
[***********3]: Unable to connect. Is the computer able to access the url?
Queue length: 182 active requests: 10
[***********2]: Unable to connect. Is the computer able to access the url?
Queue length: 181 active requests: 10
[***********5]: Failed to fetch codec info from ***********5: 404 - Not Found - {"content-length":"49","content-type":"text/html"}
Queue length: 180 active requests: 10
[***********5]: Unable to connect. Is the computer able to access the url?
Queue length: 179 active requests: 10
[***********7]: Unable to connect. Is the computer able to access the url?
Queue length: 178 active requests: 10
[***********7]: Unable to connect. Is the computer able to access the url?
Queue length: 177 active requests: 10
[***********9]: Unable to connect. Is the computer able to access the url?
Queue length: 176 active requests: 10
[***********1]: Unable to connect. Is the computer able to access the url?
Queue length: 175 active requests: 10
[***********2]: Unable to connect. Is the computer able to access the url?
Queue length: 174 active requests: 10
[***********4]: Unable to connect. Is the computer able to access the url?
Queue length: 173 active requests: 10
[***********3]: Unable to connect. Is the computer able to access the url?
Queue length: 172 active requests: 10
[***********9]: Unable to connect. Is the computer able to access the url?
Queue length: 171 active requests: 10
[***********8]: Unable to connect. Is the computer able to access the url?
Queue length: 170 active requests: 10
[192.168.2.80]: Unable to connect. Is the computer able to access the url?
Queue length: 169 active requests: 10
[192.168.2.81]: Unable to connect. Is the computer able to access the url?
Queue length: 168 active requests: 10
[192.168.2.88]: Failed to fetch codec info from 192.168.2.88: 403 - Forbidden - {"date":"Thu, 28 Aug 2025 14:08:54 GMT","expires":"Thu, 19 Nov 1981 08:52:00 GMT","cache-control":"no-store, no-cache, must-revalidate","pragma":"no-cache","content-length":"54","keep-alive":"timeout=5, max=100","connection":"Keep-Alive","content-type":"text/html; charset=UTF-8","set-cookie":["Bacula-Web=oqf29q3al2v796n80af45foqjr; expires=Thu, 28 Aug 2025 16:08:54 GMT; Max-Age=7200; path=/; HttpOnly; SameSite=Lax"],"server":"Apache/2.4.62 (Debian)"}
Queue length: 167 active requests: 10
[192.168.2.84]: Unable to connect. Is the computer able to access the url?
Queue length: 166 active requests: 10
[192.168.2.83]: Unable to connect. Is the computer able to access the url?
Queue length: 165 active requests: 10
[192.168.2.86]: Unable to connect. Is the computer able to access the url?
Queue length: 164 active requests: 10
[192.168.2.85]: Unable to connect. Is the computer able to access the url?
Queue length: 163 active requests: 10
[192.168.2.87]: Unable to connect. Is the computer able to access the url?
Queue length: 162 active requests: 10
[192.168.2.89]: Unable to connect. Is the computer able to access the url?
Queue length: 161 active requests: 10
[***********2]: Unable to connect. Is the computer able to access the url?
Queue length: 160 active requests: 10
[***********1]: Unable to connect. Is the computer able to access the url?
Queue length: 159 active requests: 10
[***********0]: Unable to connect. Is the computer able to access the url?
Queue length: 158 active requests: 10
[***********7]: Failed to fetch codec info from ***********7: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:08:59 GMT","content-length":"274","keep-alive":"timeout=5, max=100","connection":"Keep-Alive","content-type":"text/html; charset=iso-8859-1","server":"Apache/2.4.62 (Debian)"}
Queue length: 157 active requests: 10
[***********3]: Unable to connect. Is the computer able to access the url?
Queue length: 156 active requests: 10
[***********4]: Unable to connect. Is the computer able to access the url?
Queue length: 155 active requests: 10
[***********5]: Unable to connect. Is the computer able to access the url?
Queue length: 154 active requests: 10
[************0]: Unable to connect. Is the computer able to access the url?
Queue length: 153 active requests: 10
[***********6]: Unable to connect. Is the computer able to access the url?
Queue length: 152 active requests: 10
[***********9]: Unable to connect. Is the computer able to access the url?
Queue length: 151 active requests: 10
[***********8]: Unable to connect. Is the computer able to access the url?
Queue length: 150 active requests: 10
[************1]: Unable to connect. Is the computer able to access the url?
Queue length: 149 active requests: 10
[************2]: Unable to connect. Is the computer able to access the url?
Queue length: 148 active requests: 10
[************3]: Unable to connect. Is the computer able to access the url?
Queue length: 147 active requests: 10
[************4]: Unable to connect. Is the computer able to access the url?
Queue length: 146 active requests: 10
[192.168.2.110]: Unable to connect. Is the computer able to access the url?
Queue length: 145 active requests: 10
[************6]: Unable to connect. Is the computer able to access the url?
Queue length: 144 active requests: 10
[************5]: Unable to connect. Is the computer able to access the url?
Queue length: 143 active requests: 10
[192.168.2.112]: Unable to connect. Is the computer able to access the url?
Queue length: 142 active requests: 10
[************7]: Unable to connect. Is the computer able to access the url?
Queue length: 141 active requests: 10
[192.168.2.115]: Failed to fetch codec info from 192.168.2.115: 404 - Not Found - {"content-type":"text/html","date":"Thu, 28 Aug 2025 14:09:06 GMT","content-length":"1253","server":"Microsoft-IIS/10.0","x-powered-by":"ASP.NET"}
Queue length: 140 active requests: 10
[************8]: Unable to connect. Is the computer able to access the url?
Queue length: 139 active requests: 10
[************9]: Unable to connect. Is the computer able to access the url?
Queue length: 138 active requests: 10
[192.168.2.117]: Failed to fetch codec info from 192.168.2.117: 404 - Not Found - {"content-length":"49","content-type":"text/html"}
Queue length: 137 active requests: 10
[192.168.2.113]: ����5
ECC9FF468DDC{e n(�@���H���P���X���`���
Queue length: 136 active requests: 10
[192.168.2.119]: Failed to fetch codec info from 192.168.2.119: 404 - Not Found - {"content-length":"49","content-type":"text/html"}
Queue length: 135 active requests: 10
[192.168.2.111]: Unable to connect. Is the computer able to access the url?
Queue length: 134 active requests: 10
[************2]: Failed to fetch codec info from ************2: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:09:08 GMT","content-length":"0","server":"Microsoft-IIS/10.0"}
Queue length: 133 active requests: 10
[************1]: Unable to connect. Is the computer able to access the url?
Queue length: 132 active requests: 10
[192.168.2.114]: Unable to connect. Is the computer able to access the url?
Queue length: 131 active requests: 10
[************4]: Failed to fetch codec info from ************4: 404 - Not Found - {"content-length":"49","content-type":"text/html"}
Queue length: 130 active requests: 10
[192.168.2.116]: Unable to connect. Is the computer able to access the url?
Queue length: 129 active requests: 10
[192.168.2.118]: Unable to connect. Is the computer able to access the url?
Queue length: 128 active requests: 10
[************0]: Unable to connect. Is the computer able to access the url?
Queue length: 127 active requests: 10
[************3]: Unable to connect. Is the computer able to access the url?
Queue length: 126 active requests: 10
[************5]: Unable to connect. Is the computer able to access the url?
Queue length: 125 active requests: 10
[************7]: Unable to connect. Is the computer able to access the url?
Queue length: 124 active requests: 10
[************8]: Unable to connect. Is the computer able to access the url?
Queue length: 123 active requests: 10
[************3]: Failed to fetch codec info from ************3: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:09:13 GMT","content-length":"275","keep-alive":"timeout=5, max=100","connection":"Keep-Alive","content-type":"text/html; charset=iso-8859-1","server":"Apache/2.4.58 (Ubuntu)"}
Queue length: 122 active requests: 10
[************9]: Unable to connect. Is the computer able to access the url?
Queue length: 121 active requests: 10
[************0]: Unable to connect. Is the computer able to access the url?
Queue length: 120 active requests: 10
[************1]: Unable to connect. Is the computer able to access the url?
Queue length: 119 active requests: 10
[************2]: Unable to connect. Is the computer able to access the url?
Queue length: 118 active requests: 10
[************8]: Unable to connect. Is the computer able to access the url?
Queue length: 117 active requests: 10
[************4]: Unable to connect. Is the computer able to access the url?
Queue length: 116 active requests: 10
[************5]: Unable to connect. Is the computer able to access the url?
Queue length: 115 active requests: 10
[************6]: Unable to connect. Is the computer able to access the url?
Queue length: 114 active requests: 10
[************7]: Unable to connect. Is the computer able to access the url?
Queue length: 113 active requests: 10
[************9]: Unable to connect. Is the computer able to access the url?
Queue length: 112 active requests: 10
[192.168.2.140]: Unable to connect. Is the computer able to access the url?
Queue length: 111 active requests: 10
[192.168.2.145]: Failed to fetch codec info from 192.168.2.145: 404 - Not Found - {"cache-control":"no-cache","content-length":"942","content-type":"text/html","connection":"close","pragma":"no-cache","server":"debut/1.30"}
Queue length: 110 active requests: 10
[192.168.2.141]: Unable to connect. Is the computer able to access the url?
Queue length: 109 active requests: 10
[192.168.2.147]: Failed to fetch codec info from 192.168.2.147: 404 - Not Found - {"content-type":"text/plain","content-length":"14","connection":"keep-alive","keep-alive":"timeout=2000"}
Queue length: 108 active requests: 10
[192.168.2.148]: Failed to fetch codec info from 192.168.2.148: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:09:20 GMT","content-length":"275","keep-alive":"timeout=5, max=100","connection":"Keep-Alive","content-type":"text/html; charset=iso-8859-1","server":"Apache/2.4.58 (Ubuntu)"}
Queue length: 107 active requests: 10
[192.168.2.142]: Unable to connect. Is the computer able to access the url?
Queue length: 106 active requests: 10
[192.168.2.146]: self signed certificate in certificate chain
Queue length: 105 active requests: 10
[192.168.2.143]: Unable to connect. Is the computer able to access the url?
Queue length: 104 active requests: 10
[192.168.2.144]: Unable to connect. Is the computer able to access the url?
Queue length: 103 active requests: 10
[192.168.2.149]: Unable to connect. Is the computer able to access the url?
Queue length: 102 active requests: 10
[192.168.2.150]: Unable to connect. Is the computer able to access the url?
Queue length: 101 active requests: 10
[192.168.2.151]: Unable to connect. Is the computer able to access the url?
Queue length: 100 active requests: 10
[192.168.2.156]: Unable to connect. Is the computer able to access the url?
Queue length: 99 active requests: 10
[192.168.2.152]: Unable to connect. Is the computer able to access the url?
Queue length: 98 active requests: 10
[192.168.2.153]: Unable to connect. Is the computer able to access the url?
Queue length: 97 active requests: 10
[192.168.2.154]: Unable to connect. Is the computer able to access the url?
Queue length: 96 active requests: 10
[192.168.2.155]: Unable to connect. Is the computer able to access the url?
Queue length: 95 active requests: 10
[192.168.2.157]: Unable to connect. Is the computer able to access the url?
Queue length: 94 active requests: 10
[192.168.2.159]: Unable to connect. Is the computer able to access the url?
Queue length: 93 active requests: 10
[192.168.2.160]: Unable to connect. Is the computer able to access the url?
Queue length: 92 active requests: 10
[192.168.2.161]: Unable to connect. Is the computer able to access the url?
Queue length: 91 active requests: 10
[192.168.2.162]: Unable to connect. Is the computer able to access the url?
Queue length: 90 active requests: 10
[192.168.2.163]: Unable to connect. Is the computer able to access the url?
Queue length: 89 active requests: 10
[192.168.2.164]: Unable to connect. Is the computer able to access the url?
Queue length: 88 active requests: 10
[192.168.2.165]: Unable to connect. Is the computer able to access the url?
Queue length: 87 active requests: 10
[192.168.2.166]: Unable to connect. Is the computer able to access the url?
Queue length: 86 active requests: 10
[192.168.2.167]: Unable to connect. Is the computer able to access the url?
Queue length: 85 active requests: 10
[192.168.2.168]: Unable to connect. Is the computer able to access the url?
Queue length: 84 active requests: 10
[192.168.2.169]: Unable to connect. Is the computer able to access the url?
Queue length: 83 active requests: 10
[192.168.2.170]: Unable to connect. Is the computer able to access the url?
Queue length: 82 active requests: 10
[192.168.2.171]: Unable to connect. Is the computer able to access the url?
Queue length: 81 active requests: 10
[192.168.2.175]: Unable to connect. Is the computer able to access the url?
Queue length: 80 active requests: 10
[192.168.2.172]: Unable to connect. Is the computer able to access the url?
Queue length: 79 active requests: 10
[192.168.2.174]: Unable to connect. Is the computer able to access the url?
Queue length: 78 active requests: 10
[192.168.2.178]: Failed to fetch codec info from 192.168.2.178: 404 - Not Found - {"content-length":"49","content-type":"text/html"}
Queue length: 77 active requests: 10
[192.168.2.176]: Unable to connect. Is the computer able to access the url?
Queue length: 76 active requests: 10
[192.168.2.177]: Unable to connect. Is the computer able to access the url?
Queue length: 75 active requests: 10
[192.168.2.179]: Unable to connect. Is the computer able to access the url?
Queue length: 74 active requests: 10
[192.168.2.180]: Unable to connect. Is the computer able to access the url?
Queue length: 73 active requests: 10
[192.168.2.183]: Failed to fetch codec info from 192.168.2.183: 404 - Not Found - {"connection":"close","transfer-encoding":"chunked","content-type":"text/html"}
Queue length: 72 active requests: 10
[192.168.2.181]: Unable to connect. Is the computer able to access the url?
Queue length: 71 active requests: 10
[192.168.2.182]: Unable to connect. Is the computer able to access the url?
Queue length: 70 active requests: 10
[192.168.2.184]: Unable to connect. Is the computer able to access the url?
Queue length: 69 active requests: 10
[192.168.2.185]: Unable to connect. Is the computer able to access the url?
Queue length: 68 active requests: 10
[192.168.2.186]: Unable to connect. Is the computer able to access the url?
Queue length: 67 active requests: 10
[192.168.2.187]: Unable to connect. Is the computer able to access the url?
Queue length: 66 active requests: 10
[192.168.2.188]: Unable to connect. Is the computer able to access the url?
Queue length: 65 active requests: 10
[192.168.2.191]: Unable to connect. Is the computer able to access the url?
Queue length: 64 active requests: 10
[192.168.2.190]: Failed to fetch codec info from 192.168.2.190: 404 - Not Found - {"content-length":"49","content-type":"text/html"}
Queue length: 63 active requests: 10
[192.168.2.193]: Unable to connect. Is the computer able to access the url?
Queue length: 62 active requests: 10
[192.168.2.194]: Failed to fetch codec info from 192.168.2.194: 404 - Not Found - {"content-length":"49","content-type":"text/html"}
Queue length: 61 active requests: 10
[192.168.2.189]: Unable to connect. Is the computer able to access the url?
Queue length: 60 active requests: 10
[192.168.2.192]: Unable to connect. Is the computer able to access the url?
Queue length: 59 active requests: 10
[*************]: Unable to connect. Is the computer able to access the url?
Queue length: 58 active requests: 10
[*************]: Unable to connect. Is the computer able to access the url?
Queue length: 57 active requests: 10
[*************]: Unable to connect. Is the computer able to access the url?
Queue length: 56 active requests: 10
[***********00]: Failed to fetch codec info from ***********00: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:09:57 GMT","vary":"Accept-Encoding","content-encoding":"gzip","content-length":"234","keep-alive":"timeout=15, max=100","connection":"Keep-Alive","content-type":"text/html; charset=iso-8859-1","server":"Apache/2.2.16 (Debian)"}
Queue length: 55 active requests: 10
[*************]: Unable to connect. Is the computer able to access the url?
Queue length: 54 active requests: 10
[***********01]: Unable to connect. Is the computer able to access the url?
Queue length: 53 active requests: 10
[***********02]: Unable to connect. Is the computer able to access the url?
Queue length: 52 active requests: 10
[***********04]: Unable to connect. Is the computer able to access the url?
Queue length: 51 active requests: 10
[***********03]: Unable to connect. Is the computer able to access the url?
Queue length: 50 active requests: 10
[***********05]: Unable to connect. Is the computer able to access the url?
Queue length: 49 active requests: 10
[***********06]: Unable to connect. Is the computer able to access the url?
Queue length: 48 active requests: 10
[***********08]: Failed to fetch codec info from ***********08: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:10:05 GMT","content-length":"0","server":"Microsoft-IIS/10.0"}
Queue length: 47 active requests: 10
[***********07]: Unable to connect. Is the computer able to access the url?
Queue length: 46 active requests: 10
[***********10]: Failed to fetch codec info from ***********10: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:10:07 GMT","content-type":"text/html","transfer-encoding":"chunked","connection":"keep-alive","content-encoding":"gzip","server":"nginx"}
Queue length: 45 active requests: 10
[***********09]: Unable to connect. Is the computer able to access the url?
Queue length: 44 active requests: 10
[***********12]: Failed to fetch codec info from ***********12: 404 - Not Found - {"date":"Thu, 28 Aug 2025 14:09:42 GMT","content-length":"275","keep-alive":"timeout=5, max=100","connection":"Keep-Alive","content-type":"text/html; charset=iso-8859-1","server":"Apache/2.4.25 (Debian)"}
Queue length: 43 active requests: 10
[***********11]: Unable to connect. Is the computer able to access the url?
Queue length: 42 active requests: 10
