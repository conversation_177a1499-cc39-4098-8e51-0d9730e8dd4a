# Irrigation Plan State

Create a table for storing the state of irrigation plan executions.
The table will be populate from the SchedulingReportPackage messages.
```protobuf
message SchedulingReportData {
    /**
    * Index of the schedule that was executed. It is the idx of the Scheduling entry that was executed.
    */
  int32  scheduling_idx = 1;       // Índice do agendamento
  /**
  * Timestamp of the start of the schedule. It is the timestamp when the schedule was started.
  */
  uint64 start_time = 2;           // Timestamp do início do agendamento
  /**
  * Timestamp of the end of the schedule. It is the timestamp when the schedule was finished.
  */
  uint64 end_time = 3;             // Timestamp do fim do agendamento
  /**
   * Bitmask showing which sectors were activated. The first 64 bits represent the first 64 sectors, and the next 64 bits represent the next 64 sectors.
   * It is "cumulative", meaning that that once a sector is activated, it will remain activated.
   * Each bit in the bitmask corresponds to the sector order of execution. 0b00000001 means the sector programed to be executed first was activated, 0b00000011 means the sector programed to be executed first was activated, as well the second programed sector.
   */
  uint64 sector_bitmask1 = 4;      // Bitmask de setores acionados, mascara com 64bits iniciais
  uint64 sector_bitmask2 = 5;      // Bitmask de setores acionados, mascara com mais 64bits
  /**
   * Bitmask showing which sectors received fertigation. The first 64 bits represent the first 64 sectors, and the next 64 bits represent the next 64 sectors.
   * It is "cumulative", meaning that that once a sector receives fertigation, it will remain activated.
   */
  uint64 ferti_bitmask1 = 6;       // Bitmask da ferti de setores acionados, mascara com 64bits iniciais
  uint64 ferti_bitmask2 = 7;       // Bitmask da ferti de setores acionados, mascara com mais 64bits
  /**
  * Status of the water pump during the schedule. If 1, the water pump was on. If 0, the water pump was off.
  */
  bool   waterpump = 8;            // Estado da bomba de água (1 = ligou, 0 = não ligou)
  /**
  * Timestamp of the start of the backwash, if it occurred. It is the timestamp when the backwash was started.
  */
  uint64 backwash_time = 9;        // Timestamp de início da retrolavagem, se teve retrolavagem
  int32  number_of_sectors = 10;   // Número de setores do agendamento
  /**
  * Whether this schedule makes use of the water pump. If 1, the water pump should be used. If 0, the water pump should not be used.
  */
  bool   had_waterpump = 11;       // Se usou a bomba de agua
  /**
  * Whether this schedule makes use of fertigation. If 1, fertigation should be used. If 0, fertigation should not be used.
  */
  bool   had_ferti = 12;           // Se teve aplicação de fertilizante (1 = sim, 0 = não)
  uint64 time_of_resumption = 13;  // Hora do início da retomada
  int32  resumption_attempts = 14; // Número de tentativas da retomada
  /**
  * Status of the schedule. It can be one of the following:
  * 1: Completed
  */
  int32  status = 15;              // Código de status do agendamento
}

message SchedulingReportPackage {
  repeated SchedulingReportData data = 1;     // Lista de relatórios
}
```

## Directus and Database (Migrations and configuration)

We need to create a table to store the state of the irrigation plan executions. It will be called `current_irrigation_plan_state` and the irrigation_plan field will be a foreign key to the irrigation_plan table and will be unique, meaning that the table holds only the most recent state of the irrigation plan.

The table will have the following fields:
- id (uuid, primary key, same value of irrigation_plan field)
- irrigation_plan (uuid, foreign key to irrigation_plan table, unique)
- packet_date (timestamptz, when the original device status packet was recorded)
- start_time (timestamptz, when the irrigation plan execution started)
- end_time (timestamptz, when the irrigation plan execution ended) - (if end_time is null, the irrigation plan is still running) - (if end_time is not received in the protobuf message, but status is 1, then we update end_time to packet_date)
- activated_steps (a jsonb array with the id of the irrigation_plan_step records corresponding to the activated sectors)
- activated_ferti_steps (a jsonb array with the id of the irrigation_plan_step records corresponding to the sectors that received fertigation)
- waterpump_working (boolean, status of the water pump during the schedule)
- backwash_start_time (timestamptz, when the backwash started)
- uses_waterpump (boolean, whether the water pump should be used in the schedule)
- uses_ferti (boolean, whether fertigation should be used in the schedule)
- date_created (timestamptz, when this record was created)
- date_updated (timestamptz, when this record was last updated)
---

We will also create a table called `irrigation_plan_state` that will store the history of the states. It will have the same structure as `current_irrigation_plan_state` but without the unique constraint on irrigation_plan, without the date_updated field and with a composite primary key on (irrigation_plan, packet_date). It will be a TimescaleDB hypertable with monthly chunking and compression after 3 months. 
irrigation_plan_state will be populated from current_irrigation_plan_state through database triggers AFTER INSERT and UPDATE operations on current_irrigation_plan_state.
If a record conflicting on (irrigation_plan, packet_date) is inserted, it should be ignored and a warning should be logged.

We need migrations for the following:
1. Create irrigation_plan_state and current_irrigation_plan_state tables
2. Create a trigger to set current_irrigation_plan_state.id with irrigation_plan field value BEFORE INSERT
3. Create TimescaleDB hypertable (irrigation_plan_state)
4. Create trigger to populate irrigation_plan_state from current_irrigation_plan_state upserts
5. Create Directus configuration for the new tables: fields, permissions, display options, collections, etc.

## MQTT Integration

Similar to packages/mqtt-integration/src/irriganet/project-state-calculator.ts, we will create a file called packages/mqtt-integration/src/irriganet/irrigation-plan-state-calculator.ts that will contain the logic to calculate the state of the irrigation plan based on the SchedulingReportPackage message. It will not deal with database operations, just calculate the state based on the message.
The irrigation plan will be determined by the scheduling_idx field:
- the LICState type  has a  `schedules` field with the array of schedules for the LIC. Each item in the array has the irrigationPlanId field, which is the id of the irrigation plan in the irrigation_plan table.
- LICState also has a sectorSchedules field, with the array of steps of the irrigation plan. Each item has a n_order field, corresponding to the order of execution of the step and a irrigationPlanStepId field, which is the id of the irrigation plan step in irrigation_plan_step table.
- When processing the SchedulingReportPackage, we will  match the SchedulingReportData.scheduling_idx with the ord_idx field in the schedules array to get the schedule. The irrigationPlanId field of the schedule will give us the irrigation_plan field value of current_irrigation_plan_state.
- We will use the  sector_bitmask1 to determine what programed sectors were activated and find the items is sectorSchedules matching scheduling_idx and n_order. The irrigationPlanStepId field of each  sectorSchedule will give us the activated_steps field value of current_irrigation_plan_state.
- We will use the  ferti_bitmask1 to determine what programed sectors had ferti activated and find the items is sectorSchedules matching scheduling_idx and n_order. The irrigationPlanStepId field of each  sectorSchedule will give us the activated_ferti_steps field value of current_irrigation_plan_state.


Similar to packages/mqtt-integration/src/irriganet/package-processors/default-processors/status-package.ts processor, we will create a file called packages/mqtt-integration/src/irriganet/package-processors/default-processors/scheduling-report-package.ts that will contain the logic to process the SchedulingReportPackage message (using the irrigation-plan-state-calculator.ts) and store the state in the database.

Thus, we need to create the necessary database mutation in packages/mqtt-integration/src/db/mutations/irrigation-plan-state.ts to be used by the scheduling-report-package.ts processor.

---

Based on @/tasks/INSTRUCTIONS.md  and @/.claude/commands/task-file-expanding.md , create a TASKS file in @/tasks/active with one task and expanded subtasks